import { io, Socket } from 'socket.io-client';
import {
  WebSocketMessage,
  TextChunkEvent,
  ToolCallStartEvent,
  ToolCallEndEvent,
  StateUpdateEvent,
  AgentResponseEvent,
  ErrorEvent,
} from '@synapseai/types';

export interface WebSocketClientConfig {
  url: string;
  accessToken?: string;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
}

export type WebSocketEventHandler<T = any> = (data: T) => void;

export class WebSocketClient {
  private socket: Socket;
  private eventHandlers: Map<string, Set<WebSocketEventHandler>> = new Map();

  constructor(config: WebSocketClientConfig) {
    this.socket = io(config.url, {
      auth: {
        token: config.accessToken,
      },
      reconnection: config.reconnection ?? true,
      reconnectionAttempts: config.reconnectionAttempts ?? 5,
      reconnectionDelay: config.reconnectionDelay ?? 1000,
    });

    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.emit('connect', null);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.emit('disconnect', { reason });
    });

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.emit('error', { error });
    });

    // APIX protocol events
    this.socket.on('text_chunk', (data: TextChunkEvent['payload']) => {
      this.emit('text_chunk', data);
    });

    this.socket.on('tool_call_start', (data: ToolCallStartEvent['payload']) => {
      this.emit('tool_call_start', data);
    });

    this.socket.on('tool_call_end', (data: ToolCallEndEvent['payload']) => {
      this.emit('tool_call_end', data);
    });

    this.socket.on('state_update', (data: StateUpdateEvent['payload']) => {
      this.emit('state_update', data);
    });

    this.socket.on('agent_response', (data: AgentResponseEvent['payload']) => {
      this.emit('agent_response', data);
    });

    this.socket.on('session_error', (data: ErrorEvent['payload']) => {
      this.emit('session_error', data);
    });
  }

  private emit(event: string, data: any) {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach((handler) => handler(data));
    }
  }

  // Event subscription methods
  on(event: 'connect', handler: WebSocketEventHandler<null>): void;
  on(event: 'disconnect', handler: WebSocketEventHandler<{ reason: string }>): void;
  on(event: 'error', handler: WebSocketEventHandler<{ error: any }>): void;
  on(event: 'text_chunk', handler: WebSocketEventHandler<TextChunkEvent['payload']>): void;
  on(event: 'tool_call_start', handler: WebSocketEventHandler<ToolCallStartEvent['payload']>): void;
  on(event: 'tool_call_end', handler: WebSocketEventHandler<ToolCallEndEvent['payload']>): void;
  on(event: 'state_update', handler: WebSocketEventHandler<StateUpdateEvent['payload']>): void;
  on(event: 'agent_response', handler: WebSocketEventHandler<AgentResponseEvent['payload']>): void;
  on(event: 'session_error', handler: WebSocketEventHandler<ErrorEvent['payload']>): void;
  on(event: string, handler: WebSocketEventHandler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set());
    }
    this.eventHandlers.get(event)!.add(handler);
  }

  off(event: string, handler: WebSocketEventHandler) {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.delete(handler);
    }
  }

  // Session management
  joinSession(sessionId: string) {
    this.socket.emit('join_session', { sessionId });
  }

  leaveSession(sessionId: string) {
    this.socket.emit('leave_session', { sessionId });
  }

  // Send messages
  sendMessage(sessionId: string, message: string) {
    this.socket.emit('user_message', {
      sessionId,
      message,
      timestamp: new Date(),
    });
  }

  // Tool execution
  executeTool(sessionId: string, toolId: string, input: any) {
    this.socket.emit('execute_tool', {
      sessionId,
      toolId,
      input,
      timestamp: new Date(),
    });
  }

  // Agent interaction
  startAgent(sessionId: string, agentId: string, input?: any) {
    this.socket.emit('start_agent', {
      sessionId,
      agentId,
      input,
      timestamp: new Date(),
    });
  }

  stopAgent(sessionId: string) {
    this.socket.emit('stop_agent', {
      sessionId,
      timestamp: new Date(),
    });
  }

  // Connection management
  connect() {
    if (!this.socket.connected) {
      this.socket.connect();
    }
  }

  disconnect() {
    this.socket.disconnect();
  }

  isConnected(): boolean {
    return this.socket.connected;
  }

  updateAuth(accessToken: string) {
    this.socket.auth = { token: accessToken };
    if (this.socket.connected) {
      this.socket.disconnect();
      this.socket.connect();
    }
  }
}
