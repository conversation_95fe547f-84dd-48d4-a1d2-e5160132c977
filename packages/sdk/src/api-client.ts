import {
  ApiResponse,
  PaginatedResponse,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  User,
  Agent,
  Tool,
  Workflow,
  Session,
} from '@synapseai/types';

export interface ApiClientConfig {
  baseUrl: string;
  timeout?: number;
}

export class ApiClient {
  private baseUrl: string;
  private timeout: number;
  private accessToken?: string;

  constructor(config: ApiClientConfig) {
    this.baseUrl = config.baseUrl.replace(/\/$/, '');
    this.timeout = config.timeout || 10000;
  }

  setAccessToken(token: string) {
    this.accessToken = token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.accessToken) {
      headers.Authorization = `Bearer ${this.accessToken}`;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  // Authentication
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    return response.data!;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
    return response.data!;
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
    return response.data!;
  }

  async logout(): Promise<void> {
    await this.request('/auth/logout', { method: 'POST' });
  }

  // Users
  async getCurrentUser(): Promise<User> {
    const response = await this.request<User>('/users/me');
    return response.data!;
  }

  async getUsers(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<PaginatedResponse<User>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.search) searchParams.set('search', params.search);

    const response = await this.request<PaginatedResponse<User>>(
      `/users?${searchParams}`
    );
    return response.data!;
  }

  // Agents
  async getAgents(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<PaginatedResponse<Agent>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.status) searchParams.set('status', params.status);

    const response = await this.request<PaginatedResponse<Agent>>(
      `/agents?${searchParams}`
    );
    return response.data!;
  }

  async getAgent(id: string): Promise<Agent> {
    const response = await this.request<Agent>(`/agents/${id}`);
    return response.data!;
  }

  async createAgent(agent: Partial<Agent>): Promise<Agent> {
    const response = await this.request<Agent>('/agents', {
      method: 'POST',
      body: JSON.stringify(agent),
    });
    return response.data!;
  }

  async updateAgent(id: string, agent: Partial<Agent>): Promise<Agent> {
    const response = await this.request<Agent>(`/agents/${id}`, {
      method: 'PUT',
      body: JSON.stringify(agent),
    });
    return response.data!;
  }

  async deleteAgent(id: string): Promise<void> {
    await this.request(`/agents/${id}`, { method: 'DELETE' });
  }

  // Tools
  async getTools(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<PaginatedResponse<Tool>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.status) searchParams.set('status', params.status);

    const response = await this.request<PaginatedResponse<Tool>>(
      `/tools?${searchParams}`
    );
    return response.data!;
  }

  async getTool(id: string): Promise<Tool> {
    const response = await this.request<Tool>(`/tools/${id}`);
    return response.data!;
  }

  async createTool(tool: Partial<Tool>): Promise<Tool> {
    const response = await this.request<Tool>('/tools', {
      method: 'POST',
      body: JSON.stringify(tool),
    });
    return response.data!;
  }

  async updateTool(id: string, tool: Partial<Tool>): Promise<Tool> {
    const response = await this.request<Tool>(`/tools/${id}`, {
      method: 'PUT',
      body: JSON.stringify(tool),
    });
    return response.data!;
  }

  async deleteTool(id: string): Promise<void> {
    await this.request(`/tools/${id}`, { method: 'DELETE' });
  }

  async testTool(id: string, input: any): Promise<any> {
    const response = await this.request(`/tools/${id}/test`, {
      method: 'POST',
      body: JSON.stringify({ input }),
    });
    return response.data;
  }

  // Workflows
  async getWorkflows(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<PaginatedResponse<Workflow>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.status) searchParams.set('status', params.status);

    const response = await this.request<PaginatedResponse<Workflow>>(
      `/workflows?${searchParams}`
    );
    return response.data!;
  }

  async getWorkflow(id: string): Promise<Workflow> {
    const response = await this.request<Workflow>(`/workflows/${id}`);
    return response.data!;
  }

  async createWorkflow(workflow: Partial<Workflow>): Promise<Workflow> {
    const response = await this.request<Workflow>('/workflows', {
      method: 'POST',
      body: JSON.stringify(workflow),
    });
    return response.data!;
  }

  async updateWorkflow(id: string, workflow: Partial<Workflow>): Promise<Workflow> {
    const response = await this.request<Workflow>(`/workflows/${id}`, {
      method: 'PUT',
      body: JSON.stringify(workflow),
    });
    return response.data!;
  }

  async deleteWorkflow(id: string): Promise<void> {
    await this.request(`/workflows/${id}`, { method: 'DELETE' });
  }

  // Sessions
  async getSessions(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<PaginatedResponse<Session>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.status) searchParams.set('status', params.status);

    const response = await this.request<PaginatedResponse<Session>>(
      `/sessions?${searchParams}`
    );
    return response.data!;
  }

  async getSession(id: string): Promise<Session> {
    const response = await this.request<Session>(`/sessions/${id}`);
    return response.data!;
  }

  async createSession(session: Partial<Session>): Promise<Session> {
    const response = await this.request<Session>('/sessions', {
      method: 'POST',
      body: JSON.stringify(session),
    });
    return response.data!;
  }

  async endSession(id: string): Promise<void> {
    await this.request(`/sessions/${id}/end`, { method: 'POST' });
  }
}
