// User types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
  emailVerified: boolean;
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  USER = 'USER',
  VIEWER = 'VIEWER',
}

// Organization types
export interface Organization {
  id: string;
  name: string;
  slug: string;
  createdAt: Date;
  updatedAt: Date;
}

// Agent types
export interface Agent {
  id: string;
  name: string;
  description?: string;
  config: AgentConfig;
  status: AgentStatus;
  version: number;
  organizationId: string;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum AgentStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ARCHIVED = 'ARCHIVED',
}

export interface AgentConfig {
  provider: AIProvider;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  memory: MemoryConfig;
  tools: string[];
}

export enum AIProvider {
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE',
  AZURE = 'AZURE',
}

export interface MemoryConfig {
  type: MemoryType;
  maxMessages: number;
  summarization: boolean;
}

export enum MemoryType {
  BUFFER = 'BUFFER',
  SUMMARY = 'SUMMARY',
  VECTOR = 'VECTOR',
}

// Tool types
export interface Tool {
  id: string;
  name: string;
  description?: string;
  inputSchema: JSONSchema;
  outputSchema: JSONSchema;
  implementation: ToolImplementation;
  status: ToolStatus;
  version: number;
  organizationId: string;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum ToolStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  DEPRECATED = 'DEPRECATED',
  ARCHIVED = 'ARCHIVED',
}

export interface JSONSchema {
  type: string;
  properties?: Record<string, any>;
  required?: string[];
  additionalProperties?: boolean;
}

export interface ToolImplementation {
  type: ToolImplementationType;
  code?: string;
  endpoint?: string;
  method?: HTTPMethod;
  headers?: Record<string, string>;
}

export enum ToolImplementationType {
  JAVASCRIPT = 'JAVASCRIPT',
  HTTP_API = 'HTTP_API',
  WEBHOOK = 'WEBHOOK',
}

export enum HTTPMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

// Workflow types
export interface Workflow {
  id: string;
  name: string;
  description?: string;
  definition: WorkflowDefinition;
  status: WorkflowStatus;
  version: number;
  organizationId: string;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum WorkflowStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ARCHIVED = 'ARCHIVED',
}

export interface WorkflowDefinition {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  variables: Record<string, any>;
}

export interface WorkflowNode {
  id: string;
  type: WorkflowNodeType;
  position: { x: number; y: number };
  data: any;
}

export enum WorkflowNodeType {
  START = 'START',
  END = 'END',
  AGENT = 'AGENT',
  TOOL = 'TOOL',
  CONDITION = 'CONDITION',
  LOOP = 'LOOP',
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  condition?: string;
}

// Session types
export interface Session {
  id: string;
  status: SessionStatus;
  context?: any;
  metadata?: any;
  expiresAt: Date;
  organizationId: string;
  userId: string;
  agentId?: string;
  workflowId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum SessionStatus {
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED',
}

export interface SessionEvent {
  id: string;
  type: SessionEventType;
  data: any;
  timestamp: Date;
  sessionId: string;
}

export enum SessionEventType {
  TEXT_CHUNK = 'TEXT_CHUNK',
  TOOL_CALL_START = 'TOOL_CALL_START',
  TOOL_CALL_END = 'TOOL_CALL_END',
  STATE_UPDATE = 'STATE_UPDATE',
  AGENT_RESPONSE = 'AGENT_RESPONSE',
  ERROR = 'ERROR',
  USER_INPUT = 'USER_INPUT',
}

// WebSocket types
export interface WebSocketMessage {
  type: string;
  payload: any;
  sessionId?: string;
  timestamp: Date;
}

export interface TextChunkEvent {
  type: 'text_chunk';
  payload: {
    content: string;
    isComplete: boolean;
  };
}

export interface ToolCallStartEvent {
  type: 'tool_call_start';
  payload: {
    toolId: string;
    toolName: string;
    input: any;
  };
}

export interface ToolCallEndEvent {
  type: 'tool_call_end';
  payload: {
    toolId: string;
    output: any;
    success: boolean;
    error?: string;
  };
}

export interface StateUpdateEvent {
  type: 'state_update';
  payload: {
    state: any;
    changes: Record<string, any>;
  };
}

export interface AgentResponseEvent {
  type: 'agent_response';
  payload: {
    content: string;
    metadata?: any;
  };
}

export interface ErrorEvent {
  type: 'error';
  payload: {
    message: string;
    code?: string;
    details?: any;
  };
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  organizationName?: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface JWTPayload {
  sub: string;
  email: string;
  role: UserRole;
  organizationId: string;
  iat: number;
  exp: number;
}

// Audit types
export interface AuditLog {
  id: string;
  action: string;
  resource: string;
  resourceId?: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  userId: string;
  timestamp: Date;
}
