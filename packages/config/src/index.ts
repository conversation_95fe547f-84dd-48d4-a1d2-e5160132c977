import { z } from 'zod';

// Environment validation schemas
export const serverConfigSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.coerce.number().default(3001),
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url(),
  JWT_SECRET: z.string().min(32),
  JWT_REFRESH_SECRET: z.string().min(32),
  JWT_EXPIRES_IN: z.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),
  FRONTEND_URL: z.string().url().default('http://localhost:3000'),
  OPENAI_API_KEY: z.string().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  GOOGLE_API_KEY: z.string().optional(),
  AZURE_OPENAI_ENDPOINT: z.string().optional(),
  AZURE_OPENAI_API_KEY: z.string().optional(),
});

export const clientConfigSchema = z.object({
  NEXT_PUBLIC_API_URL: z.string().url().default('http://localhost:3001'),
  NEXT_PUBLIC_WS_URL: z.string().default('ws://localhost:3001'),
  NEXT_PUBLIC_APP_NAME: z.string().default('SynapseAI'),
  NEXT_PUBLIC_APP_VERSION: z.string().default('1.0.0'),
});

export type ServerConfig = z.infer<typeof serverConfigSchema>;
export type ClientConfig = z.infer<typeof clientConfigSchema>;

// Configuration validation functions
export function validateServerConfig(env: Record<string, string | undefined>): ServerConfig {
  try {
    return serverConfigSchema.parse(env);
  } catch (error) {
    console.error('Invalid server configuration:', error);
    throw new Error('Server configuration validation failed');
  }
}

export function validateClientConfig(env: Record<string, string | undefined>): ClientConfig {
  try {
    return clientConfigSchema.parse(env);
  } catch (error) {
    console.error('Invalid client configuration:', error);
    throw new Error('Client configuration validation failed');
  }
}

// Default configurations
export const defaultServerConfig: Partial<ServerConfig> = {
  NODE_ENV: 'development',
  PORT: 3001,
  JWT_EXPIRES_IN: '15m',
  JWT_REFRESH_EXPIRES_IN: '7d',
  FRONTEND_URL: 'http://localhost:3000',
};

export const defaultClientConfig: Partial<ClientConfig> = {
  NEXT_PUBLIC_API_URL: 'http://localhost:3001',
  NEXT_PUBLIC_WS_URL: 'ws://localhost:3001',
  NEXT_PUBLIC_APP_NAME: 'SynapseAI',
  NEXT_PUBLIC_APP_VERSION: '1.0.0',
};

// Feature flags
export interface FeatureFlags {
  enableAgentBuilder: boolean;
  enableToolManager: boolean;
  enableWorkflowBuilder: boolean;
  enableRealTimeChat: boolean;
  enableAuditLogs: boolean;
  enableMultiTenant: boolean;
  enableAPIRateLimit: boolean;
  enableWebSocketAuth: boolean;
}

export const defaultFeatureFlags: FeatureFlags = {
  enableAgentBuilder: true,
  enableToolManager: true,
  enableWorkflowBuilder: true,
  enableRealTimeChat: true,
  enableAuditLogs: true,
  enableMultiTenant: true,
  enableAPIRateLimit: true,
  enableWebSocketAuth: true,
};

// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout',
    ME: '/auth/me',
  },
  USERS: {
    LIST: '/users',
    GET: (id: string) => `/users/${id}`,
    UPDATE: (id: string) => `/users/${id}`,
    DELETE: (id: string) => `/users/${id}`,
  },
  AGENTS: {
    LIST: '/agents',
    GET: (id: string) => `/agents/${id}`,
    CREATE: '/agents',
    UPDATE: (id: string) => `/agents/${id}`,
    DELETE: (id: string) => `/agents/${id}`,
    TEST: (id: string) => `/agents/${id}/test`,
  },
  TOOLS: {
    LIST: '/tools',
    GET: (id: string) => `/tools/${id}`,
    CREATE: '/tools',
    UPDATE: (id: string) => `/tools/${id}`,
    DELETE: (id: string) => `/tools/${id}`,
    TEST: (id: string) => `/tools/${id}/test`,
  },
  WORKFLOWS: {
    LIST: '/workflows',
    GET: (id: string) => `/workflows/${id}`,
    CREATE: '/workflows',
    UPDATE: (id: string) => `/workflows/${id}`,
    DELETE: (id: string) => `/workflows/${id}`,
    EXECUTE: (id: string) => `/workflows/${id}/execute`,
  },
  SESSIONS: {
    LIST: '/sessions',
    GET: (id: string) => `/sessions/${id}`,
    CREATE: '/sessions',
    END: (id: string) => `/sessions/${id}/end`,
    EVENTS: (id: string) => `/sessions/${id}/events`,
  },
  HEALTH: {
    CHECK: '/health',
    READY: '/health/ready',
    LIVE: '/health/live',
  },
} as const;

// WebSocket events
export const WS_EVENTS = {
  // Connection events
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ERROR: 'error',
  
  // Session events
  JOIN_SESSION: 'join_session',
  LEAVE_SESSION: 'leave_session',
  
  // APIX protocol events
  TEXT_CHUNK: 'text_chunk',
  TOOL_CALL_START: 'tool_call_start',
  TOOL_CALL_END: 'tool_call_end',
  STATE_UPDATE: 'state_update',
  AGENT_RESPONSE: 'agent_response',
  SESSION_ERROR: 'session_error',
  
  // User interaction events
  USER_MESSAGE: 'user_message',
  EXECUTE_TOOL: 'execute_tool',
  START_AGENT: 'start_agent',
  STOP_AGENT: 'stop_agent',
} as const;

// Rate limiting configuration
export const RATE_LIMITS = {
  GLOBAL: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // requests per window
  },
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // login attempts per window
  },
  API: {
    windowMs: 60 * 1000, // 1 minute
    max: 60, // requests per minute
  },
  WEBSOCKET: {
    windowMs: 60 * 1000, // 1 minute
    max: 100, // messages per minute
  },
} as const;
