"use client";

import React, { useState, useRef, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, ZoomIn, ZoomOut, Save, Trash2 } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ip<PERSON>ontent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Node {
  id: string;
  type: "agent" | "tool" | "logic";
  label: string;
  x: number;
  y: number;
  inputs?: string[];
  outputs?: string[];
}

interface Connection {
  id: string;
  sourceId: string;
  targetId: string;
  sourceOutput?: string;
  targetInput?: string;
}

interface FlowEditorProps {
  nodes?: Node[];
  connections?: Connection[];
  onNodeSelect?: (node: Node | null) => void;
  onConnectionSelect?: (connection: Connection | null) => void;
  onNodesChange?: (nodes: Node[]) => void;
  onConnectionsChange?: (connections: Connection[]) => void;
}

const FlowEditor = ({
  nodes = [],
  connections = [],
  onNodeSelect = () => {},
  onConnectionSelect = () => {},
  onNodesChange = () => {},
  onConnectionsChange = () => {},
}: FlowEditorProps) => {
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [selectedConnection, setSelectedConnection] =
    useState<Connection | null>(null);
  const [zoom, setZoom] = useState<number>(1);
  const [pan, setPan] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });
  const [connectingFrom, setConnectingFrom] = useState<{
    node: Node;
    output: string;
  } | null>(null);

  const canvasRef = useRef<HTMLDivElement>(null);

  // Default nodes for demonstration
  const defaultNodes: Node[] = [
    {
      id: "agent1",
      type: "agent",
      label: "Customer Support Agent",
      x: 100,
      y: 100,
      outputs: ["response", "query"],
    },
    {
      id: "tool1",
      type: "tool",
      label: "Knowledge Base Search",
      x: 400,
      y: 200,
      inputs: ["query"],
      outputs: ["result"],
    },
    {
      id: "logic1",
      type: "logic",
      label: "Response Formatter",
      x: 250,
      y: 350,
      inputs: ["input1", "input2"],
      outputs: ["output"],
    },
  ];

  const defaultConnections: Connection[] = [
    {
      id: "conn1",
      sourceId: "agent1",
      targetId: "tool1",
      sourceOutput: "query",
      targetInput: "query",
    },
  ];

  const [flowNodes, setFlowNodes] = useState<Node[]>(
    nodes.length ? nodes : defaultNodes,
  );
  const [flowConnections, setFlowConnections] = useState<Connection[]>(
    connections.length ? connections : defaultConnections,
  );

  const handleNodeSelect = (node: Node) => {
    setSelectedNode(node);
    setSelectedConnection(null);
    onNodeSelect(node);
  };

  const handleConnectionSelect = (connection: Connection) => {
    setSelectedConnection(connection);
    setSelectedNode(null);
    onConnectionSelect(connection);
  };

  const handleCanvasMouseDown = (e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
      setSelectedNode(null);
      setSelectedConnection(null);
      onNodeSelect(null);
      onConnectionSelect(null);
    }
  };

  const handleCanvasMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      const dx = e.clientX - dragStart.x;
      const dy = e.clientY - dragStart.y;
      setPan({ x: pan.x + dx, y: pan.y + dy });
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };

  const handleCanvasMouseUp = () => {
    setIsDragging(false);
  };

  const handleNodeDrag = (nodeId: string, newX: number, newY: number) => {
    const updatedNodes = flowNodes.map((node) =>
      node.id === nodeId ? { ...node, x: newX, y: newY } : node,
    );
    setFlowNodes(updatedNodes);
    onNodesChange(updatedNodes);
  };

  const handleZoomIn = () => {
    setZoom(Math.min(zoom + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoom(Math.max(zoom - 0.1, 0.5));
  };

  const handleDeleteSelected = () => {
    if (selectedNode) {
      const updatedNodes = flowNodes.filter(
        (node) => node.id !== selectedNode.id,
      );
      const updatedConnections = flowConnections.filter(
        (conn) =>
          conn.sourceId !== selectedNode.id &&
          conn.targetId !== selectedNode.id,
      );
      setFlowNodes(updatedNodes);
      setFlowConnections(updatedConnections);
      setSelectedNode(null);
      onNodesChange(updatedNodes);
      onConnectionsChange(updatedConnections);
    } else if (selectedConnection) {
      const updatedConnections = flowConnections.filter(
        (conn) => conn.id !== selectedConnection.id,
      );
      setFlowConnections(updatedConnections);
      setSelectedConnection(null);
      onConnectionsChange(updatedConnections);
    }
  };

  const handleOutputPortClick = (node: Node, output: string) => {
    setConnectingFrom({ node, output });
  };

  const handleInputPortClick = (node: Node, input: string) => {
    if (connectingFrom && connectingFrom.node.id !== node.id) {
      const newConnection: Connection = {
        id: `conn-${Date.now()}`,
        sourceId: connectingFrom.node.id,
        targetId: node.id,
        sourceOutput: connectingFrom.output,
        targetInput: input,
      };

      const updatedConnections = [...flowConnections, newConnection];
      setFlowConnections(updatedConnections);
      onConnectionsChange(updatedConnections);
      setConnectingFrom(null);
    }
  };

  const handleCanvasClick = () => {
    setConnectingFrom(null);
  };

  const renderConnections = () => {
    return flowConnections.map((connection) => {
      const sourceNode = flowNodes.find(
        (node) => node.id === connection.sourceId,
      );
      const targetNode = flowNodes.find(
        (node) => node.id === connection.targetId,
      );

      if (!sourceNode || !targetNode) return null;

      // Calculate connection points
      const sourceX = sourceNode.x + 150; // Right side of source node
      const sourceY = sourceNode.y + 40; // Approximate center of node
      const targetX = targetNode.x; // Left side of target node
      const targetY = targetNode.y + 40; // Approximate center of node

      // Control points for the bezier curve
      const controlX1 = sourceX + 50;
      const controlX2 = targetX - 50;

      const isSelected = selectedConnection?.id === connection.id;

      return (
        <svg
          key={connection.id}
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
            zIndex: 1,
          }}
        >
          <path
            d={`M ${sourceX} ${sourceY} C ${controlX1} ${sourceY}, ${controlX2} ${targetY}, ${targetX} ${targetY}`}
            stroke={isSelected ? "#3b82f6" : "#64748b"}
            strokeWidth={isSelected ? 3 : 2}
            fill="none"
            style={{ pointerEvents: "stroke" }}
            onClick={() => handleConnectionSelect(connection)}
          />
          <circle
            cx={sourceX}
            cy={sourceY}
            r={4}
            fill={isSelected ? "#3b82f6" : "#64748b"}
          />
          <circle
            cx={targetX}
            cy={targetY}
            r={4}
            fill={isSelected ? "#3b82f6" : "#64748b"}
          />
        </svg>
      );
    });
  };

  const renderNodes = () => {
    return flowNodes.map((node) => {
      const isSelected = selectedNode?.id === node.id;
      const nodeTypeColors = {
        agent: { bg: "bg-blue-100", border: "border-blue-500" },
        tool: { bg: "bg-green-100", border: "border-green-500" },
        logic: { bg: "bg-purple-100", border: "border-purple-500" },
      };

      const { bg, border } = nodeTypeColors[node.type];

      return (
        <div
          key={node.id}
          className={`absolute p-3 rounded-md shadow-md w-[150px] cursor-move ${bg} ${isSelected ? `${border} border-2` : "border border-gray-300"}`}
          style={{
            left: `${node.x}px`,
            top: `${node.y}px`,
            zIndex: isSelected ? 10 : 5,
          }}
          onClick={(e) => {
            e.stopPropagation();
            handleNodeSelect(node);
          }}
          draggable
          onDragStart={(e) => {
            // Store the initial mouse position relative to the node
            const rect = e.currentTarget.getBoundingClientRect();
            const offsetX = e.clientX - rect.left;
            const offsetY = e.clientY - rect.top;
            e.dataTransfer.setData(
              "text/plain",
              JSON.stringify({ nodeId: node.id, offsetX, offsetY }),
            );
          }}
        >
          <div className="font-medium text-sm mb-2">{node.label}</div>

          {/* Input ports */}
          {node.inputs && node.inputs.length > 0 && (
            <div className="mb-2">
              {node.inputs.map((input) => (
                <div
                  key={`${node.id}-in-${input}`}
                  className="flex items-center mb-1"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleInputPortClick(node, input);
                  }}
                >
                  <div className="w-3 h-3 rounded-full bg-gray-600 mr-2 cursor-pointer hover:bg-blue-500" />
                  <span className="text-xs">{input}</span>
                </div>
              ))}
            </div>
          )}

          {/* Output ports */}
          {node.outputs && node.outputs.length > 0 && (
            <div>
              {node.outputs.map((output) => (
                <div
                  key={`${node.id}-out-${output}`}
                  className="flex items-center justify-end mb-1"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOutputPortClick(node, output);
                  }}
                >
                  <span className="text-xs">{output}</span>
                  <div className="w-3 h-3 rounded-full bg-gray-600 ml-2 cursor-pointer hover:bg-blue-500" />
                </div>
              ))}
            </div>
          )}
        </div>
      );
    });
  };

  return (
    <Card className="bg-white w-full h-full flex flex-col overflow-hidden">
      <div className="p-2 border-b flex justify-between items-center">
        <div className="flex space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Add Node</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleZoomIn}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Zoom In</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleZoomOut}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Zoom Out</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className="flex space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDeleteSelected}
                  disabled={!selectedNode && !selectedConnection}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Delete Selected</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm">
                  <Save className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Save Flow</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div
        className="flex-1 relative overflow-hidden bg-gray-50"
        ref={canvasRef}
        onMouseDown={handleCanvasMouseDown}
        onMouseMove={handleCanvasMouseMove}
        onMouseUp={handleCanvasMouseUp}
        onMouseLeave={handleCanvasMouseUp}
        onClick={handleCanvasClick}
        onDragOver={(e) => e.preventDefault()}
        onDrop={(e) => {
          e.preventDefault();
          const data = JSON.parse(e.dataTransfer.getData("text/plain"));
          const { nodeId, offsetX, offsetY } = data;

          // Calculate new position considering the offset and pan
          const newX =
            e.clientX -
            offsetX -
            canvasRef.current!.getBoundingClientRect().left -
            pan.x;
          const newY =
            e.clientY -
            offsetY -
            canvasRef.current!.getBoundingClientRect().top -
            pan.y;

          handleNodeDrag(nodeId, newX, newY);
        }}
      >
        <div
          className="absolute"
          style={{
            transform: `scale(${zoom}) translate(${pan.x}px, ${pan.y}px)`,
            transformOrigin: "0 0",
            width: "100%",
            height: "100%",
          }}
        >
          {renderConnections()}
          {renderNodes()}

          {/* Render connecting line when creating a new connection */}
          {connectingFrom && (
            <svg
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                pointerEvents: "none",
                zIndex: 1,
              }}
            >
              <line
                x1={connectingFrom.node.x + 150}
                y1={connectingFrom.node.y + 40}
                x2={pan.x}
                y2={pan.y}
                stroke="#3b82f6"
                strokeWidth={2}
                strokeDasharray="5,5"
              />
            </svg>
          )}
        </div>
      </div>

      <div className="p-2 border-t text-xs text-gray-500">
        {selectedNode
          ? `Selected: ${selectedNode.type} - ${selectedNode.label}`
          : selectedConnection
            ? `Selected: Connection ${selectedConnection.id}`
            : "Click on a node or connection to select it"}
      </div>
    </Card>
  );
};

export default FlowEditor;
