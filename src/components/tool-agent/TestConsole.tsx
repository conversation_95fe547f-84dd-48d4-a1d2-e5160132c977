"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Send, Play, AlertCircle, CheckCircle, Clock } from "lucide-react";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: Date;
}

interface LogEntry {
  id: string;
  type: "llm_call" | "tool_call" | "tool_result" | "error" | "info";
  message: string;
  details?: string;
  timestamp: Date;
}

interface TestConsoleProps {
  flowId?: string;
  onTest?: (input: string) => void;
}

const TestConsole = ({
  flowId = "test-flow",
  onTest = () => {},
}: TestConsoleProps) => {
  const [input, setInput] = useState("");
  const [isRunning, setIsRunning] = useState(false);
  const [activeTab, setActiveTab] = useState("chat");

  // Mock data for demonstration
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      role: "system",
      content:
        "Tool-Agent hybrid workflow initialized. You can start testing by sending a message.",
      timestamp: new Date(),
    },
  ]);

  const [logs, setLogs] = useState<LogEntry[]>([
    {
      id: "1",
      type: "info",
      message: "Workflow initialized",
      details: "Tool-Agent hybrid workflow ready for testing",
      timestamp: new Date(),
    },
  ]);

  const handleSendMessage = () => {
    if (!input.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);

    // Add log entry
    const logEntry: LogEntry = {
      id: Date.now().toString(),
      type: "info",
      message: "User input received",
      details: input,
      timestamp: new Date(),
    };

    setLogs((prev) => [...prev, logEntry]);

    // Clear input and simulate processing
    setInput("");
    setIsRunning(true);
    onTest(input);

    // Simulate response (in a real app, this would come from the backend)
    simulateResponse(input);
  };

  const simulateResponse = (userInput: string) => {
    // Simulate LLM call log
    setTimeout(() => {
      const llmCallLog: LogEntry = {
        id: Date.now().toString(),
        type: "llm_call",
        message: "LLM call initiated",
        details: "Sending prompt to OpenAI GPT-4",
        timestamp: new Date(),
      };
      setLogs((prev) => [...prev, llmCallLog]);
    }, 500);

    // Simulate tool call log
    setTimeout(() => {
      const toolCallLog: LogEntry = {
        id: Date.now().toString(),
        type: "tool_call",
        message: "Tool call initiated",
        details:
          'Calling DataRetriever tool with parameters: { query: "user data" }',
        timestamp: new Date(),
      };
      setLogs((prev) => [...prev, toolCallLog]);
    }, 1500);

    // Simulate tool result log
    setTimeout(() => {
      const toolResultLog: LogEntry = {
        id: Date.now().toString(),
        type: "tool_result",
        message: "Tool call completed",
        details:
          'Result: { success: true, data: { items: 3, lastUpdated: "2023-05-15" } }',
        timestamp: new Date(),
      };
      setLogs((prev) => [...prev, toolResultLog]);
    }, 2500);

    // Simulate assistant response
    setTimeout(() => {
      const assistantMessage: Message = {
        id: Date.now().toString(),
        role: "assistant",
        content: `I've analyzed your request: "${userInput}". Based on the data retrieved, there are 3 items matching your query, last updated on May 15, 2023. Would you like me to provide more details about these items?`,
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, assistantMessage]);
      setIsRunning(false);
    }, 3000);
  };

  const handleRunTest = () => {
    setMessages([
      {
        id: "1",
        role: "system",
        content:
          "Tool-Agent hybrid workflow initialized. Starting test run with sample data.",
        timestamp: new Date(),
      },
    ]);

    setLogs([
      {
        id: "1",
        type: "info",
        message: "Test run initiated",
        details: "Running workflow with sample data",
        timestamp: new Date(),
      },
    ]);

    setIsRunning(true);

    // Simulate a test run with predefined input
    simulateResponse("Run test with sample data");
  };

  const getLogIcon = (type: LogEntry["type"]) => {
    switch (type) {
      case "llm_call":
        return <Clock className="h-4 w-4 text-blue-500" />;
      case "tool_call":
        return <Play className="h-4 w-4 text-amber-500" />;
      case "tool_result":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getLogBadgeColor = (type: LogEntry["type"]) => {
    switch (type) {
      case "llm_call":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "tool_call":
        return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300";
      case "tool_result":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "error":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  return (
    <div className="flex flex-col w-full h-full bg-background border rounded-lg overflow-hidden">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-medium">Test Console</h3>
          <Badge variant="outline">{flowId}</Badge>
        </div>
        <Button onClick={handleRunTest} disabled={isRunning} size="sm">
          <Play className="h-4 w-4 mr-2" />
          Run Test
        </Button>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="flex-1 flex flex-col"
      >
        <div className="border-b px-4">
          <TabsList className="bg-transparent">
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="logs">Execution Logs</TabsTrigger>
          </TabsList>
        </div>

        <div className="flex-1 overflow-hidden">
          <TabsContent value="chat" className="h-full flex flex-col m-0 p-0">
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        message.role === "user"
                          ? "bg-primary text-primary-foreground"
                          : message.role === "system"
                            ? "bg-muted text-muted-foreground"
                            : "bg-secondary text-secondary-foreground"
                      }`}
                    >
                      <p>{message.content}</p>
                      <div className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))}
                {isRunning && (
                  <div className="flex justify-start">
                    <div className="max-w-[80%] rounded-lg p-3 bg-secondary text-secondary-foreground">
                      <div className="flex items-center space-x-2">
                        <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse delay-150"></div>
                        <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse delay-300"></div>
                        <span className="text-sm">Processing...</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>

            <div className="p-4 border-t">
              <div className="flex space-x-2">
                <Input
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Type a message to test the workflow..."
                  disabled={isRunning}
                  onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={isRunning || !input.trim()}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="logs" className="h-full m-0 p-0">
            <ScrollArea className="h-full">
              <Card className="border-0 shadow-none rounded-none">
                <CardHeader className="sticky top-0 bg-background z-10 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Execution Log
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {logs.map((log) => (
                      <div key={log.id} className="space-y-2">
                        <div className="flex items-center space-x-2">
                          {getLogIcon(log.type)}
                          <span className="font-medium">{log.message}</span>
                          <Badge
                            className={getLogBadgeColor(log.type)}
                            variant="secondary"
                          >
                            {log.type}
                          </Badge>
                          <span className="text-xs text-muted-foreground ml-auto">
                            {log.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        {log.details && (
                          <div className="pl-6 text-sm">
                            <pre className="bg-muted p-2 rounded-md overflow-x-auto">
                              {log.details}
                            </pre>
                          </div>
                        )}
                        <Separator />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </ScrollArea>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default TestConsole;
