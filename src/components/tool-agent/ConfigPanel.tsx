"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChevronDown, Save, Trash2, Plus } from "lucide-react";

interface ConfigPanelProps {
  selectedNodeType?: "agent" | "tool" | "connection" | null;
  selectedNodeId?: string | null;
  onConfigUpdate?: (config: any) => void;
}

const ConfigPanel = ({
  selectedNodeType = null,
  selectedNodeId = null,
  onConfigUpdate = () => {},
}: ConfigPanelProps) => {
  const [activeTab, setActiveTab] = useState("general");

  // Render different config panels based on selected node type
  const renderConfigContent = () => {
    if (!selectedNodeType) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center p-4 text-muted-foreground">
          <p>Select a node in the flow editor to configure its properties</p>
        </div>
      );
    }

    switch (selectedNodeType) {
      case "agent":
        return <AgentConfig onConfigUpdate={onConfigUpdate} />;
      case "tool":
        return <ToolConfig onConfigUpdate={onConfigUpdate} />;
      case "connection":
        return <ConnectionConfig onConfigUpdate={onConfigUpdate} />;
      default:
        return (
          <div className="flex flex-col items-center justify-center h-full text-center p-4 text-muted-foreground">
            <p>Select a node in the flow editor to configure its properties</p>
          </div>
        );
    }
  };

  return (
    <Card className="w-full h-full bg-background border overflow-hidden flex flex-col">
      <CardHeader className="px-4 py-3 border-b">
        <CardTitle className="text-lg flex items-center justify-between">
          <span>
            {selectedNodeType
              ? `Configure ${selectedNodeType}`
              : "Configuration Panel"}
            {selectedNodeId && (
              <span className="text-xs text-muted-foreground ml-2">
                ID: {selectedNodeId}
              </span>
            )}
          </span>
          {selectedNodeType && (
            <Button variant="ghost" size="sm">
              <Save className="h-4 w-4 mr-1" /> Save
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0 flex-1 overflow-hidden">
        {renderConfigContent()}
      </CardContent>
    </Card>
  );
};

const AgentConfig = ({
  onConfigUpdate,
}: {
  onConfigUpdate: (config: any) => void;
}) => {
  const [agentConfig, setAgentConfig] = useState({
    name: "My Agent",
    description: "This agent helps users with their tasks",
    model: "gpt-4",
    temperature: 0.7,
    systemPrompt:
      "You are a helpful assistant that provides accurate and concise information.",
    useMemory: true,
    memoryWindow: 10,
  });

  const handleChange = (field: string, value: any) => {
    const updatedConfig = { ...agentConfig, [field]: value };
    setAgentConfig(updatedConfig);
    onConfigUpdate(updatedConfig);
  };

  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-6">
        <Tabs defaultValue="general" onValueChange={(value) => {}}>
          <TabsList className="w-full">
            <TabsTrigger value="general" className="flex-1">
              General
            </TabsTrigger>
            <TabsTrigger value="prompt" className="flex-1">
              Prompt
            </TabsTrigger>
            <TabsTrigger value="memory" className="flex-1">
              Memory
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="agent-name">Name</Label>
              <Input
                id="agent-name"
                value={agentConfig.name}
                onChange={(e) => handleChange("name", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="agent-description">Description</Label>
              <Textarea
                id="agent-description"
                value={agentConfig.description}
                onChange={(e) => handleChange("description", e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="agent-model">Model</Label>
              <Select
                value={agentConfig.model}
                onValueChange={(value) => handleChange("model", value)}
              >
                <SelectTrigger id="agent-model">
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gpt-4">GPT-4</SelectItem>
                  <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                  <SelectItem value="claude-3-sonnet">
                    Claude 3 Sonnet
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="agent-temperature">
                Temperature: {agentConfig.temperature}
              </Label>
              <Input
                id="agent-temperature"
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={agentConfig.temperature}
                onChange={(e) =>
                  handleChange("temperature", parseFloat(e.target.value))
                }
              />
            </div>
          </TabsContent>

          <TabsContent value="prompt" className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="system-prompt">System Prompt</Label>
              <Textarea
                id="system-prompt"
                value={agentConfig.systemPrompt}
                onChange={(e) => handleChange("systemPrompt", e.target.value)}
                rows={10}
                className="font-mono text-sm"
              />
            </div>

            <div className="bg-muted p-3 rounded-md">
              <h4 className="text-sm font-medium mb-2">Available Variables</h4>
              <div className="grid grid-cols-2 gap-2">
                <code className="text-xs bg-background p-1 rounded">
                  {"{{user_name}}"}
                </code>
                <code className="text-xs bg-background p-1 rounded">
                  {"{{conversation_history}}"}
                </code>
                <code className="text-xs bg-background p-1 rounded">
                  {"{{tool_result}}"}
                </code>
                <code className="text-xs bg-background p-1 rounded">
                  {"{{current_date}}"}
                </code>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="memory" className="space-y-4 pt-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="use-memory"
                checked={agentConfig.useMemory}
                onCheckedChange={(checked) =>
                  handleChange("useMemory", checked)
                }
              />
              <Label htmlFor="use-memory">Enable memory</Label>
            </div>

            {agentConfig.useMemory && (
              <div className="space-y-2">
                <Label htmlFor="memory-window">
                  Memory Window (messages): {agentConfig.memoryWindow}
                </Label>
                <Input
                  id="memory-window"
                  type="range"
                  min="1"
                  max="20"
                  value={agentConfig.memoryWindow}
                  onChange={(e) =>
                    handleChange("memoryWindow", parseInt(e.target.value))
                  }
                />
                <p className="text-xs text-muted-foreground">
                  The agent will remember the last {agentConfig.memoryWindow}{" "}
                  messages in the conversation.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  );
};

const ToolConfig = ({
  onConfigUpdate,
}: {
  onConfigUpdate: (config: any) => void;
}) => {
  const [toolConfig, setToolConfig] = useState({
    name: "Data Processor",
    description: "Processes data and returns results",
    inputSchema: [
      {
        name: "data",
        type: "string",
        required: true,
        description: "The data to process",
      },
      {
        name: "options",
        type: "object",
        required: false,
        description: "Processing options",
      },
    ],
    outputSchema: [
      { name: "result", type: "string", description: "The processed result" },
      { name: "status", type: "string", description: "Processing status" },
    ],
    timeout: 5000,
    retryCount: 2,
  });

  const handleChange = (field: string, value: any) => {
    const updatedConfig = { ...toolConfig, [field]: value };
    setToolConfig(updatedConfig);
    onConfigUpdate(updatedConfig);
  };

  const addInputField = () => {
    const newInput = {
      name: "",
      type: "string",
      required: false,
      description: "",
    };
    handleChange("inputSchema", [...toolConfig.inputSchema, newInput]);
  };

  const updateInputField = (index: number, field: string, value: any) => {
    const updatedInputs = [...toolConfig.inputSchema];
    updatedInputs[index] = { ...updatedInputs[index], [field]: value };
    handleChange("inputSchema", updatedInputs);
  };

  const removeInputField = (index: number) => {
    const updatedInputs = toolConfig.inputSchema.filter((_, i) => i !== index);
    handleChange("inputSchema", updatedInputs);
  };

  const addOutputField = () => {
    const newOutput = { name: "", type: "string", description: "" };
    handleChange("outputSchema", [...toolConfig.outputSchema, newOutput]);
  };

  const updateOutputField = (index: number, field: string, value: any) => {
    const updatedOutputs = [...toolConfig.outputSchema];
    updatedOutputs[index] = { ...updatedOutputs[index], [field]: value };
    handleChange("outputSchema", updatedOutputs);
  };

  const removeOutputField = (index: number) => {
    const updatedOutputs = toolConfig.outputSchema.filter(
      (_, i) => i !== index,
    );
    handleChange("outputSchema", updatedOutputs);
  };

  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-6">
        <Tabs defaultValue="general" onValueChange={(value) => {}}>
          <TabsList className="w-full">
            <TabsTrigger value="general" className="flex-1">
              General
            </TabsTrigger>
            <TabsTrigger value="input" className="flex-1">
              Input Schema
            </TabsTrigger>
            <TabsTrigger value="output" className="flex-1">
              Output Schema
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4 pt-4">
            <div className="space-y-2">
              <Label htmlFor="tool-name">Name</Label>
              <Input
                id="tool-name"
                value={toolConfig.name}
                onChange={(e) => handleChange("name", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tool-description">Description</Label>
              <Textarea
                id="tool-description"
                value={toolConfig.description}
                onChange={(e) => handleChange("description", e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tool-timeout">Timeout (ms)</Label>
              <Input
                id="tool-timeout"
                type="number"
                value={toolConfig.timeout}
                onChange={(e) =>
                  handleChange("timeout", parseInt(e.target.value))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tool-retry">Retry Count</Label>
              <Input
                id="tool-retry"
                type="number"
                min="0"
                max="5"
                value={toolConfig.retryCount}
                onChange={(e) =>
                  handleChange("retryCount", parseInt(e.target.value))
                }
              />
            </div>
          </TabsContent>

          <TabsContent value="input" className="space-y-4 pt-4">
            <div className="space-y-4">
              {toolConfig.inputSchema.map((input, index) => (
                <div key={index} className="p-3 border rounded-md space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium">
                      Input Field {index + 1}
                    </h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeInputField(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-1">
                      <Label
                        htmlFor={`input-name-${index}`}
                        className="text-xs"
                      >
                        Name
                      </Label>
                      <Input
                        id={`input-name-${index}`}
                        value={input.name}
                        onChange={(e) =>
                          updateInputField(index, "name", e.target.value)
                        }
                      />
                    </div>

                    <div className="space-y-1">
                      <Label
                        htmlFor={`input-type-${index}`}
                        className="text-xs"
                      >
                        Type
                      </Label>
                      <Select
                        value={input.type}
                        onValueChange={(value) =>
                          updateInputField(index, "type", value)
                        }
                      >
                        <SelectTrigger id={`input-type-${index}`}>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="string">String</SelectItem>
                          <SelectItem value="number">Number</SelectItem>
                          <SelectItem value="boolean">Boolean</SelectItem>
                          <SelectItem value="object">Object</SelectItem>
                          <SelectItem value="array">Array</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id={`input-required-${index}`}
                      checked={input.required}
                      onCheckedChange={(checked) =>
                        updateInputField(index, "required", checked)
                      }
                    />
                    <Label
                      htmlFor={`input-required-${index}`}
                      className="text-xs"
                    >
                      Required
                    </Label>
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor={`input-desc-${index}`} className="text-xs">
                      Description
                    </Label>
                    <Input
                      id={`input-desc-${index}`}
                      value={input.description}
                      onChange={(e) =>
                        updateInputField(index, "description", e.target.value)
                      }
                    />
                  </div>
                </div>
              ))}

              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={addInputField}
              >
                <Plus className="h-4 w-4 mr-1" /> Add Input Field
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="output" className="space-y-4 pt-4">
            <div className="space-y-4">
              {toolConfig.outputSchema.map((output, index) => (
                <div key={index} className="p-3 border rounded-md space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium">
                      Output Field {index + 1}
                    </h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeOutputField(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-1">
                      <Label
                        htmlFor={`output-name-${index}`}
                        className="text-xs"
                      >
                        Name
                      </Label>
                      <Input
                        id={`output-name-${index}`}
                        value={output.name}
                        onChange={(e) =>
                          updateOutputField(index, "name", e.target.value)
                        }
                      />
                    </div>

                    <div className="space-y-1">
                      <Label
                        htmlFor={`output-type-${index}`}
                        className="text-xs"
                      >
                        Type
                      </Label>
                      <Select
                        value={output.type}
                        onValueChange={(value) =>
                          updateOutputField(index, "type", value)
                        }
                      >
                        <SelectTrigger id={`output-type-${index}`}>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="string">String</SelectItem>
                          <SelectItem value="number">Number</SelectItem>
                          <SelectItem value="boolean">Boolean</SelectItem>
                          <SelectItem value="object">Object</SelectItem>
                          <SelectItem value="array">Array</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor={`output-desc-${index}`} className="text-xs">
                      Description
                    </Label>
                    <Input
                      id={`output-desc-${index}`}
                      value={output.description}
                      onChange={(e) =>
                        updateOutputField(index, "description", e.target.value)
                      }
                    />
                  </div>
                </div>
              ))}

              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={addOutputField}
              >
                <Plus className="h-4 w-4 mr-1" /> Add Output Field
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  );
};

const ConnectionConfig = ({
  onConfigUpdate,
}: {
  onConfigUpdate: (config: any) => void;
}) => {
  const [connectionConfig, setConnectionConfig] = useState({
    name: "Data Flow",
    description: "Connects agent output to tool input",
    mappings: [
      { source: "agent.response", target: "tool.data", transform: "" },
    ],
    condition: "",
  });

  const handleChange = (field: string, value: any) => {
    const updatedConfig = { ...connectionConfig, [field]: value };
    setConnectionConfig(updatedConfig);
    onConfigUpdate(updatedConfig);
  };

  const addMapping = () => {
    const newMapping = { source: "", target: "", transform: "" };
    handleChange("mappings", [...connectionConfig.mappings, newMapping]);
  };

  const updateMapping = (index: number, field: string, value: string) => {
    const updatedMappings = [...connectionConfig.mappings];
    updatedMappings[index] = { ...updatedMappings[index], [field]: value };
    handleChange("mappings", updatedMappings);
  };

  const removeMapping = (index: number) => {
    const updatedMappings = connectionConfig.mappings.filter(
      (_, i) => i !== index,
    );
    handleChange("mappings", updatedMappings);
  };

  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="connection-name">Name</Label>
            <Input
              id="connection-name"
              value={connectionConfig.name}
              onChange={(e) => handleChange("name", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="connection-description">Description</Label>
            <Textarea
              id="connection-description"
              value={connectionConfig.description}
              onChange={(e) => handleChange("description", e.target.value)}
              rows={2}
            />
          </div>

          <Separator />

          <div className="space-y-2">
            <Label htmlFor="connection-condition">Condition (optional)</Label>
            <Textarea
              id="connection-condition"
              value={connectionConfig.condition}
              onChange={(e) => handleChange("condition", e.target.value)}
              placeholder="e.g. agent.response.includes('search')"
              rows={2}
              className="font-mono text-sm"
            />
            <p className="text-xs text-muted-foreground">
              JavaScript expression that determines if this connection should be
              executed. Leave empty to always execute.
            </p>
          </div>

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Parameter Mappings</h3>
              <Button variant="outline" size="sm" onClick={addMapping}>
                <Plus className="h-4 w-4 mr-1" /> Add Mapping
              </Button>
            </div>

            {connectionConfig.mappings.map((mapping, index) => (
              <div key={index} className="p-3 border rounded-md space-y-3">
                <div className="flex justify-between items-center">
                  <h4 className="text-sm font-medium">Mapping {index + 1}</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeMapping(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-3">
                  <div className="space-y-1">
                    <Label
                      htmlFor={`mapping-source-${index}`}
                      className="text-xs"
                    >
                      Source
                    </Label>
                    <Input
                      id={`mapping-source-${index}`}
                      value={mapping.source}
                      onChange={(e) =>
                        updateMapping(index, "source", e.target.value)
                      }
                      placeholder="e.g. agent.response"
                    />
                  </div>

                  <div className="space-y-1">
                    <Label
                      htmlFor={`mapping-target-${index}`}
                      className="text-xs"
                    >
                      Target
                    </Label>
                    <Input
                      id={`mapping-target-${index}`}
                      value={mapping.target}
                      onChange={(e) =>
                        updateMapping(index, "target", e.target.value)
                      }
                      placeholder="e.g. tool.data"
                    />
                  </div>

                  <div className="space-y-1">
                    <Label
                      htmlFor={`mapping-transform-${index}`}
                      className="text-xs"
                    >
                      Transform (optional)
                    </Label>
                    <Textarea
                      id={`mapping-transform-${index}`}
                      value={mapping.transform}
                      onChange={(e) =>
                        updateMapping(index, "transform", e.target.value)
                      }
                      placeholder="e.g. value.toLowerCase()"
                      rows={2}
                      className="font-mono text-sm"
                    />
                    <p className="text-xs text-muted-foreground">
                      JavaScript expression to transform the source value before
                      mapping to target. Use 'value' to reference the source
                      value.
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </ScrollArea>
  );
};

export default ConfigPanel;
