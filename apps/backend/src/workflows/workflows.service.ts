import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  Workflow,
  WorkflowStatus,
  PaginatedResponse,
  UserRole,
  WorkflowDefinition,
  WorkflowNodeType,
} from '@synapseai/types';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  GetWorkflowsDto,
  ExecuteWorkflowDto,
} from './dto/workflow.dto';

@Injectable()
export class WorkflowsService {
  constructor(private prisma: PrismaService) {}

  async findAll(
    organizationId: string,
    params: GetWorkflowsDto,
  ): Promise<PaginatedResponse<Workflow>> {
    const { page = 1, limit = 10, status } = params;
    const skip = (page - 1) * limit;

    const where = {
      organizationId,
      ...(status && { status }),
    };

    const [workflows, total] = await Promise.all([
      this.prisma.workflow.findMany({
        where,
        skip,
        take: limit,
        include: {
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { updatedAt: 'desc' },
      }),
      this.prisma.workflow.count({ where }),
    ]);

    return {
      data: workflows as Workflow[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, organizationId: string): Promise<Workflow> {
    const workflow = await this.prisma.workflow.findFirst({
      where: { id, organizationId },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!workflow) {
      throw new NotFoundException('Workflow not found');
    }

    return workflow as Workflow;
  }

  async create(
    createWorkflowDto: CreateWorkflowDto,
    organizationId: string,
    userId: string,
  ): Promise<Workflow> {
    // Validate workflow definition
    this.validateWorkflowDefinition(createWorkflowDto.definition);

    const workflow = await this.prisma.workflow.create({
      data: {
        ...createWorkflowDto,
        organizationId,
        createdById: userId,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return workflow as Workflow;
  }

  async update(
    id: string,
    updateWorkflowDto: UpdateWorkflowDto,
    organizationId: string,
    userId: string,
    userRole: UserRole,
  ): Promise<Workflow> {
    const workflow = await this.findOne(id, organizationId);

    // Only creator or admins can update
    if (
      workflow.createdById !== userId &&
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    // Validate workflow definition if provided
    if (updateWorkflowDto.definition) {
      this.validateWorkflowDefinition(updateWorkflowDto.definition);
    }

    const updatedWorkflow = await this.prisma.workflow.update({
      where: { id },
      data: {
        ...updateWorkflowDto,
        version: { increment: 1 },
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return updatedWorkflow as Workflow;
  }

  async remove(
    id: string,
    organizationId: string,
    userId: string,
    userRole: UserRole,
  ): Promise<void> {
    const workflow = await this.findOne(id, organizationId);

    // Only creator or admins can delete
    if (
      workflow.createdById !== userId &&
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    await this.prisma.workflow.delete({
      where: { id },
    });
  }

  async execute(
    id: string,
    executeWorkflowDto: ExecuteWorkflowDto,
    organizationId: string,
    userId: string,
  ): Promise<any> {
    const workflow = await this.findOne(id, organizationId);

    if (workflow.status !== WorkflowStatus.ACTIVE) {
      throw new BadRequestException('Workflow is not active');
    }

    // Create a new session for this workflow execution
    const session = await this.prisma.session.create({
      data: {
        status: 'ACTIVE',
        context: executeWorkflowDto.input || {},
        metadata: {
          workflowId: id,
          executionId: this.generateExecutionId(),
        },
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        organizationId,
        userId,
        workflowId: id,
      },
    });

    try {
      const result = await this.executeWorkflowDefinition(
        workflow.definition as WorkflowDefinition,
        executeWorkflowDto.input || {},
        session.id,
        organizationId,
      );

      await this.prisma.session.update({
        where: { id: session.id },
        data: {
          status: 'COMPLETED',
          context: result,
        },
      });

      return {
        sessionId: session.id,
        result,
        status: 'completed',
      };
    } catch (error) {
      await this.prisma.session.update({
        where: { id: session.id },
        data: {
          status: 'FAILED',
          metadata: {
            ...session.metadata,
            error: error.message,
          },
        },
      });

      throw new BadRequestException(`Workflow execution failed: ${error.message}`);
    }
  }

  private validateWorkflowDefinition(definition: WorkflowDefinition): void {
    if (!definition.nodes || !Array.isArray(definition.nodes)) {
      throw new BadRequestException('Workflow definition must have nodes array');
    }

    if (!definition.edges || !Array.isArray(definition.edges)) {
      throw new BadRequestException('Workflow definition must have edges array');
    }

    // Check for start node
    const startNodes = definition.nodes.filter(node => node.type === WorkflowNodeType.START);
    if (startNodes.length !== 1) {
      throw new BadRequestException('Workflow must have exactly one start node');
    }

    // Check for end node
    const endNodes = definition.nodes.filter(node => node.type === WorkflowNodeType.END);
    if (endNodes.length === 0) {
      throw new BadRequestException('Workflow must have at least one end node');
    }

    // Validate node IDs are unique
    const nodeIds = definition.nodes.map(node => node.id);
    const uniqueNodeIds = new Set(nodeIds);
    if (nodeIds.length !== uniqueNodeIds.size) {
      throw new BadRequestException('All node IDs must be unique');
    }

    // Validate edges reference existing nodes
    for (const edge of definition.edges) {
      if (!uniqueNodeIds.has(edge.source)) {
        throw new BadRequestException(`Edge references non-existent source node: ${edge.source}`);
      }
      if (!uniqueNodeIds.has(edge.target)) {
        throw new BadRequestException(`Edge references non-existent target node: ${edge.target}`);
      }
    }
  }

  private async executeWorkflowDefinition(
    definition: WorkflowDefinition,
    input: any,
    sessionId: string,
    organizationId: string,
  ): Promise<any> {
    // This is a simplified workflow execution engine
    // In a production system, you'd want a more sophisticated workflow engine
    
    const startNode = definition.nodes.find(node => node.type === WorkflowNodeType.START);
    if (!startNode) {
      throw new Error('No start node found');
    }

    let currentContext = { ...input, ...definition.variables };
    let currentNodeId = startNode.id;
    const executionPath: string[] = [];

    while (currentNodeId) {
      executionPath.push(currentNodeId);
      const currentNode = definition.nodes.find(node => node.id === currentNodeId);
      
      if (!currentNode) {
        throw new Error(`Node not found: ${currentNodeId}`);
      }

      // Process current node
      switch (currentNode.type) {
        case WorkflowNodeType.START:
          // Start node just passes through
          break;
        case WorkflowNodeType.END:
          // End node terminates execution
          return {
            output: currentContext,
            executionPath,
            completedAt: new Date(),
          };
        case WorkflowNodeType.AGENT:
          // Execute agent (simplified)
          currentContext = await this.executeAgentNode(currentNode, currentContext, organizationId);
          break;
        case WorkflowNodeType.TOOL:
          // Execute tool (simplified)
          currentContext = await this.executeToolNode(currentNode, currentContext, organizationId);
          break;
        case WorkflowNodeType.CONDITION:
          // Evaluate condition
          const conditionResult = this.evaluateCondition(currentNode, currentContext);
          currentContext.lastConditionResult = conditionResult;
          break;
        default:
          throw new Error(`Unsupported node type: ${currentNode.type}`);
      }

      // Find next node
      const outgoingEdges = definition.edges.filter(edge => edge.source === currentNodeId);
      
      if (outgoingEdges.length === 0) {
        // No outgoing edges, workflow ends here
        break;
      } else if (outgoingEdges.length === 1) {
        // Single path
        currentNodeId = outgoingEdges[0].target;
      } else {
        // Multiple paths, evaluate conditions
        const nextEdge = outgoingEdges.find(edge => {
          if (!edge.condition) return true; // Default path
          return this.evaluateEdgeCondition(edge.condition, currentContext);
        });
        
        if (nextEdge) {
          currentNodeId = nextEdge.target;
        } else {
          throw new Error('No valid path found from current node');
        }
      }
    }

    return {
      output: currentContext,
      executionPath,
      completedAt: new Date(),
    };
  }

  private async executeAgentNode(node: any, context: any, organizationId: string): Promise<any> {
    // Simplified agent execution
    // In a real implementation, you'd integrate with your agent execution system
    return {
      ...context,
      [`agent_${node.id}_result`]: `Agent ${node.data?.agentId || 'unknown'} executed with input: ${JSON.stringify(context)}`,
    };
  }

  private async executeToolNode(node: any, context: any, organizationId: string): Promise<any> {
    // Simplified tool execution
    // In a real implementation, you'd integrate with your tool execution system
    return {
      ...context,
      [`tool_${node.id}_result`]: `Tool ${node.data?.toolId || 'unknown'} executed with input: ${JSON.stringify(context)}`,
    };
  }

  private evaluateCondition(node: any, context: any): boolean {
    // Simplified condition evaluation
    // In a real implementation, you'd have a proper expression evaluator
    const condition = node.data?.condition;
    if (!condition) return true;
    
    try {
      // Very basic condition evaluation (unsafe for production)
      return new Function('context', `return ${condition}`)(context);
    } catch (error) {
      return false;
    }
  }

  private evaluateEdgeCondition(condition: string, context: any): boolean {
    try {
      // Very basic condition evaluation (unsafe for production)
      return new Function('context', `return ${condition}`)(context);
    } catch (error) {
      return false;
    }
  }

  private generateExecutionId(): string {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
