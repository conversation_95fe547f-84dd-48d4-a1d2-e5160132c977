import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { WorkflowsService } from './workflows.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  GetWorkflowsDto,
  ExecuteWorkflowDto,
} from './dto/workflow.dto';
import { Workflow, PaginatedResponse } from '@synapseai/types';

@ApiTags('Workflows')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('workflows')
export class WorkflowsController {
  constructor(private readonly workflowsService: WorkflowsService) {}

  @ApiOperation({ summary: 'Get all workflows' })
  @ApiResponse({ status: 200, description: 'Workflows retrieved successfully' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, type: String })
  @Get()
  findAll(
    @Request() req,
    @Query() query: GetWorkflowsDto,
  ): Promise<PaginatedResponse<Workflow>> {
    return this.workflowsService.findAll(req.user.organizationId, query);
  }

  @ApiOperation({ summary: 'Get workflow by ID' })
  @ApiResponse({ status: 200, description: 'Workflow retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req): Promise<Workflow> {
    return this.workflowsService.findOne(id, req.user.organizationId);
  }

  @ApiOperation({ summary: 'Create new workflow' })
  @ApiResponse({ status: 201, description: 'Workflow created successfully' })
  @Post()
  create(@Body() createWorkflowDto: CreateWorkflowDto, @Request() req): Promise<Workflow> {
    return this.workflowsService.create(
      createWorkflowDto,
      req.user.organizationId,
      req.user.id,
    );
  }

  @ApiOperation({ summary: 'Update workflow' })
  @ApiResponse({ status: 200, description: 'Workflow updated successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
    @Request() req,
  ): Promise<Workflow> {
    return this.workflowsService.update(
      id,
      updateWorkflowDto,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
  }

  @ApiOperation({ summary: 'Delete workflow' })
  @ApiResponse({ status: 200, description: 'Workflow deleted successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Delete(':id')
  async remove(@Param('id') id: string, @Request() req): Promise<{ message: string }> {
    await this.workflowsService.remove(
      id,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
    return { message: 'Workflow deleted successfully' };
  }

  @ApiOperation({ summary: 'Execute workflow' })
  @ApiResponse({ status: 200, description: 'Workflow executed successfully' })
  @ApiResponse({ status: 404, description: 'Workflow not found' })
  @ApiResponse({ status: 400, description: 'Workflow execution failed' })
  @Post(':id/execute')
  execute(
    @Param('id') id: string,
    @Body() executeWorkflowDto: ExecuteWorkflowDto,
    @Request() req,
  ): Promise<any> {
    return this.workflowsService.execute(
      id,
      executeWorkflowDto,
      req.user.organizationId,
      req.user.id,
    );
  }
}
