import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsObject,
  IsNumber,
  Min,
  MaxLength,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  WorkflowStatus,
  WorkflowDefinition,
} from '@synapseai/types';

export class CreateWorkflowDto {
  @ApiProperty({ example: 'Customer Onboarding Workflow' })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @ApiProperty({ example: 'Automated workflow for onboarding new customers', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    example: {
      nodes: [
        {
          id: 'start',
          type: 'START',
          position: { x: 0, y: 0 },
          data: {},
        },
        {
          id: 'agent1',
          type: 'AGENT',
          position: { x: 200, y: 0 },
          data: { agentId: 'agent-123' },
        },
        {
          id: 'end',
          type: 'END',
          position: { x: 400, y: 0 },
          data: {},
        },
      ],
      edges: [
        { id: 'e1', source: 'start', target: 'agent1' },
        { id: 'e2', source: 'agent1', target: 'end' },
      ],
      variables: {},
    },
  })
  @IsObject()
  definition: WorkflowDefinition;

  @ApiProperty({ enum: WorkflowStatus, default: WorkflowStatus.DRAFT })
  @IsOptional()
  @IsEnum(WorkflowStatus)
  status?: WorkflowStatus = WorkflowStatus.DRAFT;
}

export class UpdateWorkflowDto extends PartialType(CreateWorkflowDto) {}

export class GetWorkflowsDto {
  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({ enum: WorkflowStatus, required: false })
  @IsOptional()
  @IsEnum(WorkflowStatus)
  status?: WorkflowStatus;
}

export class ExecuteWorkflowDto {
  @ApiProperty({ example: { customerEmail: '<EMAIL>', plan: 'premium' }, required: false })
  @IsOptional()
  @IsObject()
  input?: any;
}
