import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  Tool,
  ToolStatus,
  PaginatedResponse,
  UserRole,
  ToolImplementationType,
} from '@synapseai/types';
import { CreateToolDto, UpdateToolDto, GetToolsDto, TestToolDto } from './dto/tool.dto';

@Injectable()
export class ToolsService {
  constructor(private prisma: PrismaService) {}

  async findAll(
    organizationId: string,
    params: GetToolsDto,
  ): Promise<PaginatedResponse<Tool>> {
    const { page = 1, limit = 10, status } = params;
    const skip = (page - 1) * limit;

    const where = {
      organizationId,
      ...(status && { status }),
    };

    const [tools, total] = await Promise.all([
      this.prisma.tool.findMany({
        where,
        skip,
        take: limit,
        include: {
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { updatedAt: 'desc' },
      }),
      this.prisma.tool.count({ where }),
    ]);

    return {
      data: tools as Tool[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, organizationId: string): Promise<Tool> {
    const tool = await this.prisma.tool.findFirst({
      where: { id, organizationId },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    return tool as Tool;
  }

  async create(
    createToolDto: CreateToolDto,
    organizationId: string,
    userId: string,
  ): Promise<Tool> {
    // Validate implementation based on type
    this.validateToolImplementation(createToolDto.implementation);

    const tool = await this.prisma.tool.create({
      data: {
        ...createToolDto,
        organizationId,
        createdById: userId,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return tool as Tool;
  }

  async update(
    id: string,
    updateToolDto: UpdateToolDto,
    organizationId: string,
    userId: string,
    userRole: UserRole,
  ): Promise<Tool> {
    const tool = await this.findOne(id, organizationId);

    // Only creator or admins can update
    if (
      tool.createdById !== userId &&
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    // Validate implementation if provided
    if (updateToolDto.implementation) {
      this.validateToolImplementation(updateToolDto.implementation);
    }

    const updatedTool = await this.prisma.tool.update({
      where: { id },
      data: {
        ...updateToolDto,
        version: { increment: 1 },
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return updatedTool as Tool;
  }

  async remove(
    id: string,
    organizationId: string,
    userId: string,
    userRole: UserRole,
  ): Promise<void> {
    const tool = await this.findOne(id, organizationId);

    // Only creator or admins can delete
    if (
      tool.createdById !== userId &&
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    await this.prisma.tool.delete({
      where: { id },
    });
  }

  async testTool(
    id: string,
    testData: TestToolDto,
    organizationId: string,
  ): Promise<any> {
    const tool = await this.findOne(id, organizationId);

    try {
      const result = await this.executeTool(tool, testData.input);
      return {
        success: true,
        output: result,
        executionTime: Date.now(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        executionTime: Date.now(),
      };
    }
  }

  private async executeTool(tool: Tool, input: any): Promise<any> {
    const { implementation } = tool;

    switch (implementation.type) {
      case ToolImplementationType.JAVASCRIPT:
        return this.executeJavaScriptTool(implementation.code!, input);
      case ToolImplementationType.HTTP_API:
        return this.executeHttpApiTool(implementation, input);
      case ToolImplementationType.WEBHOOK:
        throw new BadRequestException('Webhook tools cannot be tested directly');
      default:
        throw new BadRequestException('Unsupported tool implementation type');
    }
  }

  private async executeJavaScriptTool(code: string, input: any): Promise<any> {
    try {
      // Create a safe execution context
      const func = new Function('input', `
        const console = { log: () => {}, error: () => {}, warn: () => {} };
        const process = undefined;
        const require = undefined;
        const module = undefined;
        const exports = undefined;
        const global = undefined;
        const globalThis = undefined;
        
        ${code}
      `);
      
      return func(input);
    } catch (error) {
      throw new BadRequestException(`JavaScript execution error: ${error.message}`);
    }
  }

  private async executeHttpApiTool(implementation: any, input: any): Promise<any> {
    const { endpoint, method = 'POST', headers = {} } = implementation;

    try {
      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
        body: method !== 'GET' ? JSON.stringify(input) : undefined,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      throw new BadRequestException(`HTTP API error: ${error.message}`);
    }
  }

  private validateToolImplementation(implementation: any): void {
    if (!implementation.type) {
      throw new BadRequestException('Implementation type is required');
    }

    switch (implementation.type) {
      case ToolImplementationType.JAVASCRIPT:
        if (!implementation.code) {
          throw new BadRequestException('JavaScript code is required');
        }
        break;
      case ToolImplementationType.HTTP_API:
        if (!implementation.endpoint) {
          throw new BadRequestException('API endpoint is required');
        }
        try {
          new URL(implementation.endpoint);
        } catch {
          throw new BadRequestException('Invalid API endpoint URL');
        }
        break;
      case ToolImplementationType.WEBHOOK:
        if (!implementation.endpoint) {
          throw new BadRequestException('Webhook endpoint is required');
        }
        try {
          new URL(implementation.endpoint);
        } catch {
          throw new BadRequestException('Invalid webhook endpoint URL');
        }
        break;
      default:
        throw new BadRequestException('Unsupported implementation type');
    }
  }
}
