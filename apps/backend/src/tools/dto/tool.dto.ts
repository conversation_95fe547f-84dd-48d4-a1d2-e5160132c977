import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsObject,
  IsNumber,
  Min,
  MaxLength,
  <PERSON><PERSON>ength,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  ToolStatus,
  JSONSchema,
  ToolImplementation,
  ToolImplementationType,
} from '@synapseai/types';

export class CreateToolDto {
  @ApiProperty({ example: 'Weather API Tool' })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @ApiProperty({ example: 'Get current weather for a location', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    example: {
      type: 'object',
      properties: {
        location: {
          type: 'string',
          description: 'City name or coordinates',
        },
      },
      required: ['location'],
    },
  })
  @IsObject()
  inputSchema: JSONSchema;

  @ApiProperty({
    example: {
      type: 'object',
      properties: {
        temperature: { type: 'number' },
        humidity: { type: 'number' },
        description: { type: 'string' },
      },
    },
  })
  @IsObject()
  outputSchema: JSONSchema;

  @ApiProperty({
    example: {
      type: 'HTTP_API',
      endpoint: 'https://api.weather.com/v1/current',
      method: 'GET',
      headers: {
        'X-API-Key': 'your-api-key',
      },
    },
  })
  @IsObject()
  implementation: ToolImplementation;

  @ApiProperty({ enum: ToolStatus, default: ToolStatus.DRAFT })
  @IsOptional()
  @IsEnum(ToolStatus)
  status?: ToolStatus = ToolStatus.DRAFT;
}

export class UpdateToolDto extends PartialType(CreateToolDto) {}

export class GetToolsDto {
  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({ enum: ToolStatus, required: false })
  @IsOptional()
  @IsEnum(ToolStatus)
  status?: ToolStatus;
}

export class TestToolDto {
  @ApiProperty({ example: { location: 'New York' } })
  @IsObject()
  input: any;
}
