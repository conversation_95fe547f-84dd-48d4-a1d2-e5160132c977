import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { ToolsService } from './tools.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  CreateToolDto,
  UpdateToolDto,
  GetToolsDto,
  TestToolDto,
} from './dto/tool.dto';
import { Tool, PaginatedResponse } from '@synapseai/types';

@ApiTags('Tools')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('tools')
export class ToolsController {
  constructor(private readonly toolsService: ToolsService) {}

  @ApiOperation({ summary: 'Get all tools' })
  @ApiResponse({ status: 200, description: 'Tools retrieved successfully' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, type: String })
  @Get()
  findAll(
    @Request() req,
    @Query() query: GetToolsDto,
  ): Promise<PaginatedResponse<Tool>> {
    return this.toolsService.findAll(req.user.organizationId, query);
  }

  @ApiOperation({ summary: 'Get tool by ID' })
  @ApiResponse({ status: 200, description: 'Tool retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req): Promise<Tool> {
    return this.toolsService.findOne(id, req.user.organizationId);
  }

  @ApiOperation({ summary: 'Create new tool' })
  @ApiResponse({ status: 201, description: 'Tool created successfully' })
  @Post()
  create(@Body() createToolDto: CreateToolDto, @Request() req): Promise<Tool> {
    return this.toolsService.create(
      createToolDto,
      req.user.organizationId,
      req.user.id,
    );
  }

  @ApiOperation({ summary: 'Update tool' })
  @ApiResponse({ status: 200, description: 'Tool updated successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateToolDto: UpdateToolDto,
    @Request() req,
  ): Promise<Tool> {
    return this.toolsService.update(
      id,
      updateToolDto,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
  }

  @ApiOperation({ summary: 'Delete tool' })
  @ApiResponse({ status: 200, description: 'Tool deleted successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req): Promise<void> {
    return this.toolsService.remove(
      id,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
  }

  @ApiOperation({ summary: 'Test tool execution' })
  @ApiResponse({ status: 200, description: 'Tool tested successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @Post(':id/test')
  testTool(
    @Param('id') id: string,
    @Body() testToolDto: TestToolDto,
    @Request() req,
  ): Promise<any> {
    return this.toolsService.testTool(id, testToolDto, req.user.organizationId);
  }
}
