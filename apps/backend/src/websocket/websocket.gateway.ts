import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger, UseGuards, Injectable } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { RedisService } from '../redis/redis.service';
import {
  WebSocketMessage,
  TextChunkEvent,
  ToolCallStartEvent,
  ToolCallEndEvent,
  StateUpdateEvent,
  AgentResponseEvent,
  ErrorEvent,
  SessionEventType,
  JWTPayload,
} from '@synapseai/types';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  organizationId?: string;
  sessionId?: string;
}

@Injectable()
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/ws',
})
export class WebSocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebSocketGateway.name);
  private connectedClients = new Map<string, AuthenticatedSocket>();

  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
    private prisma: PrismaService,
    private redis: RedisService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('WebSocket Gateway initialized');
    this.setupRedisSubscriptions();
  }

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token = this.extractTokenFromHandshake(client);
      if (!token) {
        this.logger.warn(`Client ${client.id} connected without token`);
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      }) as JWTPayload;

      // Verify user exists and is active
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        select: { id: true, isActive: true, organizationId: true },
      });

      if (!user || !user.isActive) {
        this.logger.warn(`Invalid user ${payload.sub} attempted connection`);
        client.disconnect();
        return;
      }

      client.userId = user.id;
      client.organizationId = user.organizationId;
      this.connectedClients.set(client.id, client);

      // Join organization room
      client.join(`org:${user.organizationId}`);

      this.logger.log(
        `Client ${client.id} connected for user ${user.id} in org ${user.organizationId}`,
      );

      // Send connection confirmation
      client.emit('connected', {
        message: 'Connected successfully',
        userId: user.id,
        organizationId: user.organizationId,
      });
    } catch (error) {
      this.logger.error(`Connection error for client ${client.id}:`, error);
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    this.connectedClients.delete(client.id);
    if (client.sessionId) {
      client.leave(`session:${client.sessionId}`);
    }
    if (client.organizationId) {
      client.leave(`org:${client.organizationId}`);
    }
    this.logger.log(`Client ${client.id} disconnected`);
  }

  @SubscribeMessage('join_session')
  async handleJoinSession(
    @MessageBody() data: { sessionId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      // Verify session exists and user has access
      const session = await this.prisma.session.findFirst({
        where: {
          id: data.sessionId,
          organizationId: client.organizationId,
        },
      });

      if (!session) {
        client.emit('error', {
          type: 'error',
          payload: {
            message: 'Session not found or access denied',
            code: 'SESSION_NOT_FOUND',
          },
        });
        return;
      }

      // Leave previous session if any
      if (client.sessionId) {
        client.leave(`session:${client.sessionId}`);
      }

      // Join new session
      client.sessionId = data.sessionId;
      client.join(`session:${data.sessionId}`);

      this.logger.log(
        `Client ${client.id} joined session ${data.sessionId}`,
      );

      client.emit('session_joined', {
        sessionId: data.sessionId,
        message: 'Successfully joined session',
      });
    } catch (error) {
      this.logger.error('Error joining session:', error);
      client.emit('error', {
        type: 'error',
        payload: {
          message: 'Failed to join session',
          code: 'JOIN_SESSION_ERROR',
        },
      });
    }
  }

  @SubscribeMessage('leave_session')
  handleLeaveSession(@ConnectedSocket() client: AuthenticatedSocket) {
    if (client.sessionId) {
      client.leave(`session:${client.sessionId}`);
      const sessionId = client.sessionId;
      client.sessionId = undefined;
      
      this.logger.log(
        `Client ${client.id} left session ${sessionId}`,
      );

      client.emit('session_left', {
        sessionId,
        message: 'Successfully left session',
      });
    }
  }

  @SubscribeMessage('user_input')
  async handleUserInput(
    @MessageBody() data: { content: string; metadata?: any },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    if (!client.sessionId) {
      client.emit('error', {
        type: 'error',
        payload: {
          message: 'No active session',
          code: 'NO_ACTIVE_SESSION',
        },
      });
      return;
    }

    try {
      // Store user input event
      await this.prisma.sessionEvent.create({
        data: {
          type: SessionEventType.USER_INPUT,
          data: {
            content: data.content,
            metadata: data.metadata,
          },
          sessionId: client.sessionId,
          timestamp: new Date(),
        },
      });

      // Broadcast to session participants
      this.server.to(`session:${client.sessionId}`).emit('user_input', {
        type: 'user_input',
        payload: {
          content: data.content,
          userId: client.userId,
          metadata: data.metadata,
        },
        sessionId: client.sessionId,
        timestamp: new Date(),
      });

      this.logger.log(
        `User input received from ${client.userId} in session ${client.sessionId}`,
      );
    } catch (error) {
      this.logger.error('Error handling user input:', error);
      client.emit('error', {
        type: 'error',
        payload: {
          message: 'Failed to process user input',
          code: 'USER_INPUT_ERROR',
        },
      });
    }
  }

  // Public methods for broadcasting events
  async broadcastTextChunk(sessionId: string, event: TextChunkEvent) {
    await this.storeAndBroadcastEvent(sessionId, event);
  }

  async broadcastToolCallStart(sessionId: string, event: ToolCallStartEvent) {
    await this.storeAndBroadcastEvent(sessionId, event);
  }

  async broadcastToolCallEnd(sessionId: string, event: ToolCallEndEvent) {
    await this.storeAndBroadcastEvent(sessionId, event);
  }

  async broadcastStateUpdate(sessionId: string, event: StateUpdateEvent) {
    await this.storeAndBroadcastEvent(sessionId, event);
  }

  async broadcastAgentResponse(sessionId: string, event: AgentResponseEvent) {
    await this.storeAndBroadcastEvent(sessionId, event);
  }

  async broadcastError(sessionId: string, event: ErrorEvent) {
    await this.storeAndBroadcastEvent(sessionId, event);
  }

  private async storeAndBroadcastEvent(
    sessionId: string,
    event: WebSocketMessage,
  ) {
    try {
      // Store event in database
      await this.prisma.sessionEvent.create({
        data: {
          type: event.type as SessionEventType,
          data: event.payload,
          sessionId,
          timestamp: new Date(),
        },
      });

      // Store in Redis for real-time access
      await this.redis.publish(`session:${sessionId}`, event);

      // Broadcast to WebSocket clients
      this.server.to(`session:${sessionId}`).emit(event.type, {
        ...event,
        sessionId,
        timestamp: new Date(),
      });

      this.logger.debug(
        `Broadcasted ${event.type} event to session ${sessionId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error broadcasting event ${event.type} to session ${sessionId}:`,
        error,
      );
    }
  }

  private async setupRedisSubscriptions() {
    try {
      // Subscribe to session events from Redis
      await this.redis.subscribe('session:*', (message) => {
        this.handleRedisMessage(message);
      });

      this.logger.log('Redis subscriptions established');
    } catch (error) {
      this.logger.error('Error setting up Redis subscriptions:', error);
    }
  }

  private handleRedisMessage(message: any) {
    try {
      if (message.sessionId) {
        this.server.to(`session:${message.sessionId}`).emit(message.type, message);
      }
    } catch (error) {
      this.logger.error('Error handling Redis message:', error);
    }
  }

  private extractTokenFromHandshake(client: Socket): string | null {
    const token = client.handshake.auth?.token || 
                 client.handshake.headers?.authorization?.replace('Bearer ', '');
    return token || null;
  }

  // Utility methods for external services
  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  getSessionParticipants(sessionId: string): string[] {
    const participants: string[] = [];
    this.connectedClients.forEach((client) => {
      if (client.sessionId === sessionId) {
        participants.push(client.userId!);
      }
    });
    return participants;
  }

  async disconnectUser(userId: string, reason?: string) {
    this.connectedClients.forEach((client) => {
      if (client.userId === userId) {
        if (reason) {
          client.emit('disconnected', { reason });
        }
        client.disconnect();
      }
    });
  }
}
