import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { RedisService } from '../redis/redis.service';
import {
  Session,
  SessionStatus,
  SessionEvent,
  SessionEventType,
  PaginatedResponse,
  UserRole,
} from '@synapseai/types';
import {
  CreateSessionDto,
  UpdateSessionDto,
  GetSessionsDto,
  CreateSessionEventDto,
} from './dto/session.dto';

@Injectable()
export class SessionsService {
  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
  ) {}

  async findAll(
    organizationId: string,
    params: GetSessionsDto,
  ): Promise<PaginatedResponse<Session>> {
    const { page = 1, limit = 10, status, userId } = params;
    const skip = (page - 1) * limit;

    const where = {
      organizationId,
      ...(status && { status }),
      ...(userId && { userId }),
    };

    const [sessions, total] = await Promise.all([
      this.prisma.session.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          agent: {
            select: {
              id: true,
              name: true,
            },
          },
          workflow: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.session.count({ where }),
    ]);

    return {
      data: sessions as Session[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(
    id: string,
    organizationId: string,
    requestingUserId: string,
    requestingUserRole: UserRole,
  ): Promise<Session> {
    const session = await this.prisma.session.findFirst({
      where: { id, organizationId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        agent: {
          select: {
            id: true,
            name: true,
            config: true,
          },
        },
        workflow: {
          select: {
            id: true,
            name: true,
            definition: true,
          },
        },
      },
    });

    if (!session) {
      throw new NotFoundException('Session not found');
    }

    // Users can only view their own sessions unless they're admin
    if (
      session.userId !== requestingUserId &&
      requestingUserRole !== UserRole.ADMIN &&
      requestingUserRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    return session as Session;
  }

  async create(
    createSessionDto: CreateSessionDto,
    organizationId: string,
    userId: string,
  ): Promise<Session> {
    // Validate agent or workflow exists if provided
    if (createSessionDto.agentId) {
      const agent = await this.prisma.agent.findFirst({
        where: { id: createSessionDto.agentId, organizationId },
      });
      if (!agent) {
        throw new BadRequestException('Agent not found');
      }
    }

    if (createSessionDto.workflowId) {
      const workflow = await this.prisma.workflow.findFirst({
        where: { id: createSessionDto.workflowId, organizationId },
      });
      if (!workflow) {
        throw new BadRequestException('Workflow not found');
      }
    }

    const session = await this.prisma.session.create({
      data: {
        ...createSessionDto,
        organizationId,
        userId,
        status: SessionStatus.ACTIVE,
        expiresAt: createSessionDto.expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours default
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        agent: {
          select: {
            id: true,
            name: true,
          },
        },
        workflow: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Store session in Redis for quick access
    await this.storeSessionInRedis(session.id, session);

    return session as Session;
  }

  async update(
    id: string,
    updateSessionDto: UpdateSessionDto,
    organizationId: string,
    requestingUserId: string,
    requestingUserRole: UserRole,
  ): Promise<Session> {
    const session = await this.findOne(id, organizationId, requestingUserId, requestingUserRole);

    const updatedSession = await this.prisma.session.update({
      where: { id },
      data: updateSessionDto,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        agent: {
          select: {
            id: true,
            name: true,
          },
        },
        workflow: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Update session in Redis
    await this.storeSessionInRedis(id, updatedSession);

    return updatedSession as Session;
  }

  async remove(
    id: string,
    organizationId: string,
    requestingUserId: string,
    requestingUserRole: UserRole,
  ): Promise<void> {
    const session = await this.findOne(id, organizationId, requestingUserId, requestingUserRole);

    await this.prisma.session.delete({
      where: { id },
    });

    // Remove from Redis
    await this.redis.del(`session:${id}`);
  }

  async getSessionEvents(
    sessionId: string,
    organizationId: string,
    requestingUserId: string,
    requestingUserRole: UserRole,
  ): Promise<SessionEvent[]> {
    // Verify session access
    await this.findOne(sessionId, organizationId, requestingUserId, requestingUserRole);

    const events = await this.prisma.sessionEvent.findMany({
      where: { sessionId },
      orderBy: { timestamp: 'asc' },
    });

    return events as SessionEvent[];
  }

  async createSessionEvent(
    sessionId: string,
    createEventDto: CreateSessionEventDto,
    organizationId: string,
    requestingUserId: string,
    requestingUserRole: UserRole,
  ): Promise<SessionEvent> {
    // Verify session access
    await this.findOne(sessionId, organizationId, requestingUserId, requestingUserRole);

    const event = await this.prisma.sessionEvent.create({
      data: {
        ...createEventDto,
        sessionId,
        timestamp: new Date(),
      },
    });

    // Store event in Redis for real-time access
    await this.redis.lpush(
      `session_events:${sessionId}`,
      JSON.stringify(event)
    );

    // Keep only last 1000 events in Redis
    await this.redis.ltrim(`session_events:${sessionId}`, 0, 999);

    return event as SessionEvent;
  }

  async getActiveSessionsCount(organizationId: string): Promise<number> {
    return this.prisma.session.count({
      where: {
        organizationId,
        status: SessionStatus.ACTIVE,
        expiresAt: {
          gt: new Date(),
        },
      },
    });
  }

  async expireSessions(): Promise<void> {
    const expiredSessions = await this.prisma.session.findMany({
      where: {
        status: SessionStatus.ACTIVE,
        expiresAt: {
          lt: new Date(),
        },
      },
      select: { id: true },
    });

    if (expiredSessions.length > 0) {
      const sessionIds = expiredSessions.map(s => s.id);
      
      await this.prisma.session.updateMany({
        where: {
          id: { in: sessionIds },
        },
        data: {
          status: SessionStatus.EXPIRED,
        },
      });

      // Remove from Redis
      for (const sessionId of sessionIds) {
        await this.redis.del(`session:${sessionId}`);
        await this.redis.del(`session_events:${sessionId}`);
      }
    }
  }

  async getSessionFromRedis(sessionId: string): Promise<Session | null> {
    const sessionData = await this.redis.get(`session:${sessionId}`);
    return sessionData ? JSON.parse(sessionData) : null;
  }

  private async storeSessionInRedis(sessionId: string, session: any): Promise<void> {
    const ttl = Math.floor((new Date(session.expiresAt).getTime() - Date.now()) / 1000);
    if (ttl > 0) {
      await this.redis.setex(`session:${sessionId}`, ttl, JSON.stringify(session));
    }
  }
}
