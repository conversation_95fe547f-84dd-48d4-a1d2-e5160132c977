import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { SessionsService } from './sessions.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  CreateSessionDto,
  UpdateSessionDto,
  GetSessionsDto,
  CreateSessionEventDto,
} from './dto/session.dto';
import { Session, SessionEvent, PaginatedResponse } from '@synapseai/types';

@ApiTags('Sessions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('sessions')
export class SessionsController {
  constructor(private readonly sessionsService: SessionsService) {}

  @ApiOperation({ summary: 'Get all sessions' })
  @ApiResponse({ status: 200, description: 'Sessions retrieved successfully' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'userId', required: false, type: String })
  @Get()
  findAll(
    @Request() req,
    @Query() query: GetSessionsDto,
  ): Promise<PaginatedResponse<Session>> {
    return this.sessionsService.findAll(req.user.organizationId, query);
  }

  @ApiOperation({ summary: 'Get session by ID' })
  @ApiResponse({ status: 200, description: 'Session retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req): Promise<Session> {
    return this.sessionsService.findOne(
      id,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
  }

  @ApiOperation({ summary: 'Create new session' })
  @ApiResponse({ status: 201, description: 'Session created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid session data' })
  @Post()
  create(@Body() createSessionDto: CreateSessionDto, @Request() req): Promise<Session> {
    return this.sessionsService.create(
      createSessionDto,
      req.user.organizationId,
      req.user.id,
    );
  }

  @ApiOperation({ summary: 'Update session' })
  @ApiResponse({ status: 200, description: 'Session updated successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateSessionDto: UpdateSessionDto,
    @Request() req,
  ): Promise<Session> {
    return this.sessionsService.update(
      id,
      updateSessionDto,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
  }

  @ApiOperation({ summary: 'Delete session' })
  @ApiResponse({ status: 200, description: 'Session deleted successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Delete(':id')
  async remove(@Param('id') id: string, @Request() req): Promise<{ message: string }> {
    await this.sessionsService.remove(
      id,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
    return { message: 'Session deleted successfully' };
  }

  @ApiOperation({ summary: 'Get session events' })
  @ApiResponse({ status: 200, description: 'Session events retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Get(':id/events')
  getSessionEvents(
    @Param('id') id: string,
    @Request() req,
  ): Promise<SessionEvent[]> {
    return this.sessionsService.getSessionEvents(
      id,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
  }

  @ApiOperation({ summary: 'Create session event' })
  @ApiResponse({ status: 201, description: 'Session event created successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Post(':id/events')
  createSessionEvent(
    @Param('id') id: string,
    @Body() createEventDto: CreateSessionEventDto,
    @Request() req,
  ): Promise<SessionEvent> {
    return this.sessionsService.createSessionEvent(
      id,
      createEventDto,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
  }

  @ApiOperation({ summary: 'Get active sessions count' })
  @ApiResponse({ status: 200, description: 'Active sessions count retrieved successfully' })
  @Get('stats/active-count')
  async getActiveSessionsCount(@Request() req): Promise<{ count: number }> {
    const count = await this.sessionsService.getActiveSessionsCount(
      req.user.organizationId,
    );
    return { count };
  }

  @ApiOperation({ summary: 'Expire old sessions' })
  @ApiResponse({ status: 200, description: 'Sessions expired successfully' })
  @Post('maintenance/expire')
  async expireSessions(): Promise<{ message: string }> {
    await this.sessionsService.expireSessions();
    return { message: 'Expired sessions cleaned up successfully' };
  }
}
