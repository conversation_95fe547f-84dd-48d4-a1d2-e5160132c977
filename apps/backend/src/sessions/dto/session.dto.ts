import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsObject,
  IsNumber,
  Min,
  IsDateString,
  IsUUID,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  SessionStatus,
  SessionEventType,
} from '@synapseai/types';

export class CreateSessionDto {
  @ApiProperty({ required: false, example: { initialData: 'value' } })
  @IsOptional()
  @IsObject()
  context?: any;

  @ApiProperty({ required: false, example: { source: 'web', version: '1.0' } })
  @IsOptional()
  @IsObject()
  metadata?: any;

  @ApiProperty({ required: false, example: '2024-12-31T23:59:59Z' })
  @IsOptional()
  @IsDateString()
  expiresAt?: Date;

  @ApiProperty({ required: false, example: 'agent-uuid-123' })
  @IsOptional()
  @IsUUID()
  agentId?: string;

  @ApiProperty({ required: false, example: 'workflow-uuid-123' })
  @IsOptional()
  @IsUUID()
  workflowId?: string;
}

export class UpdateSessionDto extends PartialType(CreateSessionDto) {
  @ApiProperty({ enum: SessionStatus, required: false })
  @IsOptional()
  @IsEnum(SessionStatus)
  status?: SessionStatus;
}

export class GetSessionsDto {
  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({ enum: SessionStatus, required: false })
  @IsOptional()
  @IsEnum(SessionStatus)
  status?: SessionStatus;

  @ApiProperty({ required: false, example: 'user-uuid-123' })
  @IsOptional()
  @IsUUID()
  userId?: string;
}

export class CreateSessionEventDto {
  @ApiProperty({ enum: SessionEventType, example: SessionEventType.USER_INPUT })
  @IsEnum(SessionEventType)
  type: SessionEventType;

  @ApiProperty({ 
    example: { 
      content: 'Hello, how can I help you?', 
      metadata: { source: 'user' } 
    } 
  })
  @IsObject()
  data: any;
}

export class SessionStatsDto {
  @ApiProperty({ required: false, example: '2024-01-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ required: false, example: '2024-12-31T23:59:59Z' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({ required: false, example: 'daily' })
  @IsOptional()
  @IsString()
  groupBy?: 'daily' | 'weekly' | 'monthly';
}
