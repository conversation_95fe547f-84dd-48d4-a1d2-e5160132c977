import { Injectable } from '@nestjs/common';
import { HealthIndicatorResult, HealthIndicator } from '@nestjs/terminus';
import { PrismaService } from '../prisma/prisma.service';
import { RedisService } from '../redis/redis.service';

@Injectable()
export class HealthService extends HealthIndicator {
  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
  ) {
    super();
  }

  async isDatabaseHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return this.getStatus(key, true, { message: 'Database is healthy' });
    } catch (error) {
      return this.getStatus(key, false, { 
        message: 'Database is unhealthy',
        error: error.message,
      });
    }
  }

  async isRedisHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const client = this.redis.getClient();
      await client.ping();
      return this.getStatus(key, true, { message: 'Redis is healthy' });
    } catch (error) {
      return this.getStatus(key, false, {
        message: 'Redis is unhealthy',
        error: error.message,
      });
    }
  }

  async getDetailedHealthInfo() {
    const startTime = Date.now();
    
    try {
      // Database metrics
      const dbStart = Date.now();
      await this.prisma.$queryRaw`SELECT 1`;
      const dbResponseTime = Date.now() - dbStart;

      // Redis metrics
      const redisStart = Date.now();
      const client = this.redis.getClient();
      await client.ping();
      const redisResponseTime = Date.now() - redisStart;

      // System metrics
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      // Application metrics
      const uptime = process.uptime();
      const version = process.env.npm_package_version || '1.0.0';
      const nodeVersion = process.version;
      const platform = process.platform;
      const arch = process.arch;

      // Database connection info
      const dbInfo = await this.getDatabaseInfo();
      
      // Redis info
      const redisInfo = await this.getRedisInfo();

      return {
        timestamp: new Date().toISOString(),
        uptime: uptime,
        version: version,
        environment: process.env.NODE_ENV || 'development',
        system: {
          nodeVersion,
          platform,
          arch,
          pid: process.pid,
        },
        memory: {
          rss: memoryUsage.rss,
          heapTotal: memoryUsage.heapTotal,
          heapUsed: memoryUsage.heapUsed,
          external: memoryUsage.external,
          arrayBuffers: memoryUsage.arrayBuffers,
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
        database: {
          status: 'healthy',
          responseTime: dbResponseTime,
          ...dbInfo,
        },
        redis: {
          status: 'healthy',
          responseTime: redisResponseTime,
          ...redisInfo,
        },
        totalResponseTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        timestamp: new Date().toISOString(),
        error: error.message,
        totalResponseTime: Date.now() - startTime,
      };
    }
  }

  private async getDatabaseInfo() {
    try {
      // Get database version and basic stats
      const result = await this.prisma.$queryRaw`
        SELECT version() as version, 
               current_database() as database,
               current_user as user
      ` as any[];
      
      return {
        version: result[0]?.version || 'unknown',
        database: result[0]?.database || 'unknown',
        user: result[0]?.user || 'unknown',
      };
    } catch (error) {
      return {
        error: error.message,
      };
    }
  }

  private async getRedisInfo() {
    try {
      const client = this.redis.getClient();
      const info = await client.info();
      
      // Parse Redis info string
      const infoLines = info.split('\r\n');
      const redisVersion = infoLines.find(line => line.startsWith('redis_version:'))?.split(':')[1];
      const connectedClients = infoLines.find(line => line.startsWith('connected_clients:'))?.split(':')[1];
      const usedMemory = infoLines.find(line => line.startsWith('used_memory_human:'))?.split(':')[1];
      
      return {
        version: redisVersion || 'unknown',
        connectedClients: connectedClients || '0',
        usedMemory: usedMemory || 'unknown',
      };
    } catch (error) {
      return {
        error: error.message,
      };
    }
  }
}
