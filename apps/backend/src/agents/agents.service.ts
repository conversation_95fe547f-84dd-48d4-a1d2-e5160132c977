import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  Agent,
  AgentStatus,
  PaginatedResponse,
  UserRole,
} from '@synapseai/types';
import { CreateAgentDto, UpdateAgentDto, GetAgentsDto } from './dto/agent.dto';

@Injectable()
export class AgentsService {
  constructor(private prisma: PrismaService) {}

  async findAll(
    organizationId: string,
    params: GetAgentsDto,
  ): Promise<PaginatedResponse<Agent>> {
    const { page = 1, limit = 10, status } = params;
    const skip = (page - 1) * limit;

    const where = {
      organizationId,
      ...(status && { status }),
    };

    const [agents, total] = await Promise.all([
      this.prisma.agent.findMany({
        where,
        skip,
        take: limit,
        include: {
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          tools: {
            include: {
              tool: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  status: true,
                },
              },
            },
          },
        },
        orderBy: { updatedAt: 'desc' },
      }),
      this.prisma.agent.count({ where }),
    ]);

    return {
      data: agents as Agent[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, organizationId: string): Promise<Agent> {
    const agent = await this.prisma.agent.findFirst({
      where: { id, organizationId },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        tools: {
          include: {
            tool: {
              select: {
                id: true,
                name: true,
                description: true,
                inputSchema: true,
                outputSchema: true,
                status: true,
              },
            },
          },
        },
      },
    });

    if (!agent) {
      throw new NotFoundException('Agent not found');
    }

    return agent as Agent;
  }

  async create(
    createAgentDto: CreateAgentDto,
    organizationId: string,
    userId: string,
  ): Promise<Agent> {
    const agent = await this.prisma.agent.create({
      data: {
        ...createAgentDto,
        organizationId,
        createdById: userId,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        tools: {
          include: {
            tool: {
              select: {
                id: true,
                name: true,
                description: true,
                status: true,
              },
            },
          },
        },
      },
    });

    return agent as Agent;
  }

  async update(
    id: string,
    updateAgentDto: UpdateAgentDto,
    organizationId: string,
    userId: string,
    userRole: UserRole,
  ): Promise<Agent> {
    const agent = await this.findOne(id, organizationId);

    // Only creator or admins can update
    if (
      agent.createdById !== userId &&
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    const updatedAgent = await this.prisma.agent.update({
      where: { id },
      data: {
        ...updateAgentDto,
        version: { increment: 1 },
      },
      include: {
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        tools: {
          include: {
            tool: {
              select: {
                id: true,
                name: true,
                description: true,
                status: true,
              },
            },
          },
        },
      },
    });

    return updatedAgent as Agent;
  }

  async remove(
    id: string,
    organizationId: string,
    userId: string,
    userRole: UserRole,
  ): Promise<void> {
    const agent = await this.findOne(id, organizationId);

    // Only creator or admins can delete
    if (
      agent.createdById !== userId &&
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    await this.prisma.agent.delete({
      where: { id },
    });
  }

  async assignTool(
    agentId: string,
    toolId: string,
    organizationId: string,
    userId: string,
    userRole: UserRole,
    config?: any,
  ): Promise<void> {
    const agent = await this.findOne(agentId, organizationId);

    // Only creator or admins can assign tools
    if (
      agent.createdById !== userId &&
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    // Verify tool exists and belongs to organization
    const tool = await this.prisma.tool.findFirst({
      where: { id: toolId, organizationId },
    });

    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    await this.prisma.agentTool.upsert({
      where: {
        agentId_toolId: {
          agentId,
          toolId,
        },
      },
      update: { config },
      create: {
        agentId,
        toolId,
        config,
      },
    });
  }

  async removeTool(
    agentId: string,
    toolId: string,
    organizationId: string,
    userId: string,
    userRole: UserRole,
  ): Promise<void> {
    const agent = await this.findOne(agentId, organizationId);

    // Only creator or admins can remove tools
    if (
      agent.createdById !== userId &&
      userRole !== UserRole.ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    await this.prisma.agentTool.delete({
      where: {
        agentId_toolId: {
          agentId,
          toolId,
        },
      },
    });
  }
}
