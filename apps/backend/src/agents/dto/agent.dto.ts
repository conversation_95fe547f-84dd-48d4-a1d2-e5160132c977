import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsObject,
  IsNumber,
  Min,
  MaxLength,
  MinLength,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { AgentStatus, AgentConfig } from '@synapseai/types';

export class CreateAgentDto {
  @ApiProperty({ example: 'Customer Support Agent' })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @ApiProperty({ example: 'AI agent for handling customer inquiries', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    example: {
      provider: 'OPENAI',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 2000,
      systemPrompt: 'You are a helpful customer support agent.',
      memory: {
        type: 'BUFFER',
        maxMessages: 10,
        summarization: false,
      },
      tools: [],
    },
  })
  @IsObject()
  config: AgentConfig;

  @ApiProperty({ enum: AgentStatus, default: AgentStatus.DRAFT })
  @IsOptional()
  @IsEnum(AgentStatus)
  status?: AgentStatus = AgentStatus.DRAFT;
}

export class UpdateAgentDto extends PartialType(CreateAgentDto) {}

export class GetAgentsDto {
  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({ enum: AgentStatus, required: false })
  @IsOptional()
  @IsEnum(AgentStatus)
  status?: AgentStatus;
}

export class AssignToolDto {
  @ApiProperty({ example: 'tool-id-123' })
  @IsString()
  toolId: string;

  @ApiProperty({ required: false, example: { timeout: 30000 } })
  @IsOptional()
  @IsObject()
  config?: any;
}
