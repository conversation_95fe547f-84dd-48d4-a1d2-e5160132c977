import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { AgentsService } from './agents.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  CreateAgentDto,
  UpdateAgentDto,
  GetAgentsDto,
  AssignToolDto,
} from './dto/agent.dto';
import { Agent, PaginatedResponse } from '@synapseai/types';

@ApiTags('Agents')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('agents')
export class AgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @ApiOperation({ summary: 'Get all agents' })
  @ApiResponse({ status: 200, description: 'Agents retrieved successfully' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, type: String })
  @Get()
  findAll(
    @Request() req,
    @Query() query: GetAgentsDto,
  ): Promise<PaginatedResponse<Agent>> {
    return this.agentsService.findAll(req.user.organizationId, query);
  }

  @ApiOperation({ summary: 'Get agent by ID' })
  @ApiResponse({ status: 200, description: 'Agent retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req): Promise<Agent> {
    return this.agentsService.findOne(id, req.user.organizationId);
  }

  @ApiOperation({ summary: 'Create new agent' })
  @ApiResponse({ status: 201, description: 'Agent created successfully' })
  @Post()
  create(@Body() createAgentDto: CreateAgentDto, @Request() req): Promise<Agent> {
    return this.agentsService.create(
      createAgentDto,
      req.user.organizationId,
      req.user.id,
    );
  }

  @ApiOperation({ summary: 'Update agent' })
  @ApiResponse({ status: 200, description: 'Agent updated successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateAgentDto: UpdateAgentDto,
    @Request() req,
  ): Promise<Agent> {
    return this.agentsService.update(
      id,
      updateAgentDto,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
  }

  @ApiOperation({ summary: 'Delete agent' })
  @ApiResponse({ status: 200, description: 'Agent deleted successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req): Promise<void> {
    return this.agentsService.remove(
      id,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
  }

  @ApiOperation({ summary: 'Assign tool to agent' })
  @ApiResponse({ status: 200, description: 'Tool assigned successfully' })
  @ApiResponse({ status: 404, description: 'Agent or tool not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Post(':id/tools')
  assignTool(
    @Param('id') id: string,
    @Body() assignToolDto: AssignToolDto,
    @Request() req,
  ): Promise<void> {
    return this.agentsService.assignTool(
      id,
      assignToolDto.toolId,
      req.user.organizationId,
      req.user.id,
      req.user.role,
      assignToolDto.config,
    );
  }

  @ApiOperation({ summary: 'Remove tool from agent' })
  @ApiResponse({ status: 200, description: 'Tool removed successfully' })
  @ApiResponse({ status: 404, description: 'Agent or tool not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @Delete(':id/tools/:toolId')
  removeTool(
    @Param('id') id: string,
    @Param('toolId') toolId: string,
    @Request() req,
  ): Promise<void> {
    return this.agentsService.removeTool(
      id,
      toolId,
      req.user.organizationId,
      req.user.id,
      req.user.role,
    );
  }
}
