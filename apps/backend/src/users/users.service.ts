import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  ConflictException,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { PrismaService } from '../prisma/prisma.service';
import {
  User,
  UserRole,
  PaginatedResponse,
} from '@synapseai/types';
import {
  CreateUserDto,
  UpdateUserDto,
  GetUsersDto,
  UpdateProfileDto,
  ChangePasswordDto,
} from './dto/user.dto';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findAll(
    organizationId: string,
    params: GetUsersDto,
  ): Promise<PaginatedResponse<User>> {
    const { page = 1, limit = 10, role, isActive } = params;
    const skip = (page - 1) * limit;

    const where = {
      organizationId,
      ...(role && { role }),
      ...(isActive !== undefined && { isActive }),
    };

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where,
        skip,
        take: limit,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          isActive: true,
          emailVerified: true,
          organizationId: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.user.count({ where }),
    ]);

    return {
      data: users as User[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findById(id: string): Promise<User | null> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        emailVerified: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return user as User | null;
  }

  async findByEmail(email: string): Promise<User | null> {
    const user = await this.prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        emailVerified: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return user as User | null;
  }

  async findOne(
    id: string,
    organizationId: string,
    requestingUserId: string,
    requestingUserRole: UserRole,
  ): Promise<User> {
    const user = await this.prisma.user.findFirst({
      where: { id, organizationId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        emailVerified: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Users can only view their own profile unless they're admin
    if (
      id !== requestingUserId &&
      requestingUserRole !== UserRole.ADMIN &&
      requestingUserRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    return user as User;
  }

  async create(
    createUserDto: CreateUserDto,
    organizationId: string,
    requestingUserRole: UserRole,
  ): Promise<User> {
    // Only admins can create users
    if (
      requestingUserRole !== UserRole.ADMIN &&
      requestingUserRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    const existingUser = await this.prisma.user.findUnique({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    const hashedPassword = await bcrypt.hash(createUserDto.password, 12);

    const user = await this.prisma.user.create({
      data: {
        ...createUserDto,
        password: hashedPassword,
        organizationId,
        emailVerified: false,
        isActive: true,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        emailVerified: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return user as User;
  }

  async update(
    id: string,
    updateUserDto: UpdateUserDto,
    organizationId: string,
    requestingUserId: string,
    requestingUserRole: UserRole,
  ): Promise<User> {
    const user = await this.findOne(id, organizationId, requestingUserId, requestingUserRole);

    // Users can only update their own profile unless they're admin
    if (
      id !== requestingUserId &&
      requestingUserRole !== UserRole.ADMIN &&
      requestingUserRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    // Only admins can change roles
    if (
      updateUserDto.role &&
      requestingUserRole !== UserRole.ADMIN &&
      requestingUserRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions to change role');
    }

    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: updateUserDto,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        emailVerified: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return updatedUser as User;
  }

  async updateProfile(
    userId: string,
    updateProfileDto: UpdateProfileDto,
  ): Promise<User> {
    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: {
        firstName: updateProfileDto.firstName,
        lastName: updateProfileDto.lastName,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        emailVerified: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return updatedUser as User;
  }

  async changePassword(
    userId: string,
    changePasswordDto: ChangePasswordDto,
  ): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const isCurrentPasswordValid = await bcrypt.compare(
      changePasswordDto.currentPassword,
      user.password,
    );

    if (!isCurrentPasswordValid) {
      throw new ForbiddenException('Current password is incorrect');
    }

    const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, 12);

    await this.prisma.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword },
    });
  }

  async remove(
    id: string,
    organizationId: string,
    requestingUserId: string,
    requestingUserRole: UserRole,
  ): Promise<void> {
    const user = await this.findOne(id, organizationId, requestingUserId, requestingUserRole);

    // Only admins can delete users, and users cannot delete themselves
    if (
      requestingUserRole !== UserRole.ADMIN &&
      requestingUserRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    if (id === requestingUserId) {
      throw new ForbiddenException('Cannot delete your own account');
    }

    await this.prisma.user.delete({
      where: { id },
    });
  }

  async deactivate(
    id: string,
    organizationId: string,
    requestingUserId: string,
    requestingUserRole: UserRole,
  ): Promise<User> {
    const user = await this.findOne(id, organizationId, requestingUserId, requestingUserRole);

    // Only admins can deactivate users
    if (
      requestingUserRole !== UserRole.ADMIN &&
      requestingUserRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException('Insufficient permissions');
    }

    if (id === requestingUserId) {
      throw new ForbiddenException('Cannot deactivate your own account');
    }

    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: { isActive: false },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        emailVerified: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return updatedUser as User;
  }
}
