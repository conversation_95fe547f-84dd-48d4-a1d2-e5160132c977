// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Organization model for multi-tenancy
model Organization {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users     User[]
  agents    Agent[]
  tools     Tool[]
  workflows Workflow[]
  sessions  Session[]

  @@map("organizations")
}

// User model with role-based access control
model User {
  id              String   @id @default(cuid())
  email           String   @unique
  password        String
  firstName       String
  lastName        String
  role            UserRole @default(USER)
  isActive        Boolean  @default(true)
  emailVerified   Boolean  @default(false)
  organizationId  String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdAgents   Agent[]      @relation("AgentCreator")
  createdTools    Tool[]       @relation("ToolCreator")
  createdWorkflows Workflow[]  @relation("WorkflowCreator")
  sessions        Session[]
  auditLogs       AuditLog[]

  @@map("users")
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  USER
  VIEWER
}

// Agent model for AI agents
model Agent {
  id             String      @id @default(cuid())
  name           String
  description    String?
  config         Json        // AgentConfig as JSON
  status         AgentStatus @default(DRAFT)
  version        Int         @default(1)
  organizationId String
  createdById    String
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdBy      User         @relation("AgentCreator", fields: [createdById], references: [id])
  tools          AgentTool[]
  sessions       Session[]

  @@map("agents")
}

enum AgentStatus {
  DRAFT
  ACTIVE
  INACTIVE
  ARCHIVED
}

// Tool model for reusable tools
model Tool {
  id             String           @id @default(cuid())
  name           String
  description    String?
  inputSchema    Json             // JSONSchema as JSON
  outputSchema   Json             // JSONSchema as JSON
  implementation Json             // ToolImplementation as JSON
  status         ToolStatus       @default(DRAFT)
  version        Int              @default(1)
  organizationId String
  createdById    String
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  // Relations
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdBy      User             @relation("ToolCreator", fields: [createdById], references: [id])
  agents         AgentTool[]

  @@map("tools")
}

enum ToolStatus {
  DRAFT
  PUBLISHED
  DEPRECATED
  ARCHIVED
}

// Junction table for Agent-Tool relationships
model AgentTool {
  id      String @id @default(cuid())
  agentId String
  toolId  String
  config  Json?  // Tool-specific configuration

  // Relations
  agent   Agent  @relation(fields: [agentId], references: [id], onDelete: Cascade)
  tool    Tool   @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@unique([agentId, toolId])
  @@map("agent_tools")
}

// Workflow model for complex automation
model Workflow {
  id             String         @id @default(cuid())
  name           String
  description    String?
  definition     Json           // WorkflowDefinition as JSON
  status         WorkflowStatus @default(DRAFT)
  version        Int            @default(1)
  organizationId String
  createdById    String
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdBy      User           @relation("WorkflowCreator", fields: [createdById], references: [id])
  sessions       Session[]

  @@map("workflows")
}

enum WorkflowStatus {
  DRAFT
  ACTIVE
  INACTIVE
  ARCHIVED
}

// Session model for execution tracking
model Session {
  id             String        @id @default(cuid())
  status         SessionStatus @default(ACTIVE)
  context        Json?         // Session context data
  metadata       Json?         // Additional metadata
  expiresAt      DateTime
  organizationId String
  userId         String
  agentId        String?
  workflowId     String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  // Relations
  organization   Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User          @relation(fields: [userId], references: [id])
  agent          Agent?        @relation(fields: [agentId], references: [id])
  workflow       Workflow?     @relation(fields: [workflowId], references: [id])
  events         SessionEvent[]

  @@map("sessions")
}

enum SessionStatus {
  ACTIVE
  COMPLETED
  FAILED
  EXPIRED
}

// Session events for real-time tracking
model SessionEvent {
  id        String           @id @default(cuid())
  type      SessionEventType
  data      Json             // Event payload
  timestamp DateTime         @default(now())
  sessionId String

  // Relations
  session   Session          @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("session_events")
}

enum SessionEventType {
  TEXT_CHUNK
  TOOL_CALL_START
  TOOL_CALL_END
  STATE_UPDATE
  AGENT_RESPONSE
  ERROR
  USER_INPUT
}

// Audit log for security and compliance
model AuditLog {
  id         String   @id @default(cuid())
  action     String
  resource   String
  resourceId String?
  metadata   Json?
  ipAddress  String?
  userAgent  String?
  userId     String
  timestamp  DateTime @default(now())

  // Relations
  user       User     @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}
