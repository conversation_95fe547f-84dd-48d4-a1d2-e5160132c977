{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "forceConsistentCasingInFileNames": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@synapseai/types": ["./packages/types/src"], "@synapseai/sdk": ["./packages/sdk/src"], "@synapseai/config": ["./packages/config/src"], "@synapseai/ui": ["./packages/ui/src"], "@synapseai/utils": ["./packages/utils/src"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", ".next"]}